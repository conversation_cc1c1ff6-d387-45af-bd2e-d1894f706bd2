import React from 'react';

import { useSignals } from '@preact/signals-react/runtime';
import { BottomTabBar } from '@react-navigation/bottom-tabs';
import { Icon } from '@rneui/themed';
import { BlurView } from 'expo-blur';
import { Tabs } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';
import { View } from 'react-native';

import BlurredHeader from '@/components/global/BlurredHeader';
import { PaywallBottomTab } from '@/components/global/Paywall';
import { useTheme } from '@/context/ThemeContext';
import { isPaywallVisible } from '@/signals/app.signal';

export default function TabLayout() {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();
  useSignals();

  return (
    <Tabs
      tabBar={props => {
        return (
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
            }}
          >
            {isPaywallVisible.value && <PaywallBottomTab />}
            <BottomTabBar {...props} />
          </View>
        );
      }}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ color, size }) => {
          let iconName;
          const iconMap: { [key: string]: string } = {
            portfolios: 'account-balance',
            dashboard: 'analytics',
            profile: 'person',
          };
          iconName = iconMap[route.name];
          return <Icon name={iconName!} type="material" size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.tabIconDefault,
        header: props => <BlurredHeader title={t(`common.tabs.${route.name}`)} {...props} />,
        tabBarBackground: () => (
          <BlurView
            experimentalBlurMethod="dimezisBlurView"
            tint={isDark ? 'dark' : 'light'}
            intensity={80}
            style={{
              ...StyleSheet.absoluteFillObject,
              overflow: 'hidden',
              backgroundColor: 'transparent',
            }}
          />
        ),
        tabBarStyle: {
          backgroundColor: 'transparent',
          borderTopColor: colors.background,
          borderTopWidth: 0,
        },
      })}
    >
      <Tabs.Screen
        name="dashboard"
        options={{
          title: t('common.tabs.dashboard'),
        }}
      />
      <Tabs.Screen
        name="portfolios"
        options={{
          title: t('common.tabs.portfolios'),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('common.tabs.profile'),
        }}
      />
    </Tabs>
  );
}
