import { StatusBar } from 'expo-status-bar';

import { useUserProfile } from '@/common/profile';
import { withUserProfile } from '@/common/withUserProfile';
import FullScreenActivityIndicator from '@/components/FullScreenActivityIndicator';
import { JsStack } from '@/components/global/JsStack';
import { ModalProvider } from '@/components/global/modal';
import { useTheme } from '@/context/ThemeContext';
import { RevenueCatProvider } from '@/hooks/usePurchases';

function Layout() {
  const { isLight } = useTheme();
  const { profile } = useUserProfile();

  if (!profile) return <FullScreenActivityIndicator />;

  return (
    <RevenueCatProvider>
      <ModalProvider>
        <StatusBar style={isLight ? 'dark' : 'light'} />
        <JsStack initialRouteName="app" screenOptions={{ headerShown: false, animationEnabled: false }}>
          <JsStack.Screen name="onboarding" />
          <JsStack.Screen name="settings" options={{ presentation: 'modal' }} />
          <JsStack.Screen name="company" options={{ headerShown: false }} />
        </JsStack>
      </ModalProvider>
    </RevenueCatProvider>
  );
}

export default withUserProfile(Layout);
