import React from 'react';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Slot } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { SnackbarProvider } from '@/components/global/Snackbar';
import { FontsProvider, useFontsLoaded } from '@/context/FontsProvider';
import { ThemeProviderContext } from '@/context/ThemeContext';
import '@/i18n';

export const queryClient = new QueryClient();

function ThemedStatusBar() {
  return <StatusBar translucent backgroundColor="transparent" style="auto" />;
}

export default function Layout() {
  return (
    <FontsProvider>
      <LayoutWithFonts />
    </FontsProvider>
  );
}

function LayoutWithFonts() {
  const loaded = useFontsLoaded();
  if (!loaded) return null;
  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <GestureHandlerRootView>
          <ThemeProviderContext>
            <SnackbarProvider>
              <Slot />
              <ThemedStatusBar />
            </SnackbarProvider>
          </ThemeProviderContext>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
}
