import React from 'react';

import { Icon } from '@rneui/themed';
import { capitalize } from 'lodash';
import { useTranslation } from 'react-i18next';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';

import { clsx } from '@/common/clsx';
import { useUserProfileQuery } from '@/common/queries';
import { SelectModal } from '@/components/base/SelectModal';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { UserProfileDepot } from '@/types/depot.types';
import { scaleFont } from '@/utils/scaler';

import { BankParentIcon } from './BankParentIcon';

interface SelectPortfolioProps {
  selectedPortfolios?: UserProfileDepot[];
  onSelect: (portfolios: UserProfileDepot[]) => void;
  multiple?: boolean;
  style?: StyleProp<ViewStyle>;
  placeholder?: string;
  disabled?: boolean;
}

export const SelectPortfolio: React.FC<SelectPortfolioProps> = ({
  selectedPortfolios = [],
  onSelect,
  multiple = true,
  style,
  placeholder,
  disabled = false,
}) => {
  const { colors, isLight, isDark } = useTheme();

  const { t } = useTranslation();
  const { data } = useUserProfileQuery();
  const portfolios = data?.depots?.filter(d => !d.isClone) ?? [];

  return (
    <SelectModal
      style={style}
      items={portfolios}
      selectedItems={selectedPortfolios}
      onSelect={onSelect}
      multiple={multiple}
      disabled={disabled}
      labelExtractor={portfolio => portfolio.bankName ?? 'Portfolio'}
      keyExtractor={portfolio => portfolio.id}
      modalTitle="Pick portfolios"
      placeholder={placeholder ?? 'Select portfolios'}
      searchPlaceholder="Search for portfolios..."
      noResultsText="No portfolios found"
      renderItem={(portfolio, isSelected, onPress) => (
        <TouchableOpacity onPress={onPress} className={clsx('flex-row items-center py-3 px-0 mb-2')}>
          {/* Left side - Checkbox and Bank Icon */}
          <View className="flex-row items-center mr-3">
            <Icon
              name={isSelected ? 'check-circle' : 'radio-button-unchecked'}
              type="material"
              size={20}
              color={isSelected ? colors.primary : colors.tabIconDefault}
              style={{ marginRight: 12 }}
            />
            <BankParentIcon bankParent={portfolio.bankType} size={40} />
          </View>

          {/* Middle - Portfolio Info */}
          <View className="flex-1 ml-3">
            <ThemedText className="font-medium text-base mb-1">{capitalize(portfolio.bankName)}</ThemedText>
            <ThemedText className="text-gray-500 text-sm">
              {portfolio.number || portfolio.description || portfolio.id}
            </ThemedText>
          </View>

          {/* Right side - Arrow */}
          <Icon name="chevron-right" type="material" size={scaleFont(20)} color={colors.tabIconDefault} />
        </TouchableOpacity>
      )}
    />
  );
};
