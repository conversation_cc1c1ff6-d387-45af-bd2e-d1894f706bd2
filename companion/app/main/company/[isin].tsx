// filepath: /home/<USER>/Desktop/companion/components/features/actor/TopThreeTable/TopThreeTable.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface TopThreeTableProps {
  data: { id: string; name: string; value: string }[];
}

const TopThreeTable: React.FC<TopThreeTableProps> = ({ data }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Top Three Items</Text>
      {data.map(item => (
        <View key={item.id} style={styles.row}>
          <Text style={styles.cell}>{item.name}</Text>
          <Text style={styles.cell}>{item.value}</Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  cell: {
    fontSize: 16,
  },
});

export default TopThreeTable;

// filepath: /home/<USER>/Desktop/companion/components/features/actor/TopThreeTable/index.ts
export { default } from './TopThreeTable';

// filepath: /home/<USER>/Desktop/companion/app/main/company/[isin].tsx
import React from 'react';
import { Icon } from '@rneui/themed';
import { useQuery } from '@tanstack/react-query';
import { router, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, ScrollView, TouchableOpacity, View } from 'react-native';

import { apiGet } from '@/common/api';
import {
  CompanyDividendEvolutionWidget,
  CompanyDividendHistoryWidget,
  CompanyDividendTableWidget,
  CompanyDividendYieldWidget,
  CompanyIsinWknWidget,
  CompanyQuotesWidget,
  CompanySectorsWidget,
  CompanySharePriceWidget,
} from '@/components/features/actor';
import CompanyHeader from '@/components/features/actor/CompanyHeader';
import DividendsChart from '@/components/features/actor/DividendsChart';
import InvestmentChart from '@/components/features/actor/InvestmentChart';
import { Header } from '@/components/global/Header';
import { ThemedCard } from '@/components/global/ThemedCard';
import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import useInitializeActor from '@/hooks/useInitializeActor';
import { ActorService } from '@/services/actor.service';
import { CompanyDividendData, DividendDisplayOption, SecurityAccountSecurity } from '@/types/actor-api.types';
import { SecurityAccountSecurityType } from '@/types/secapi.types';
import { scaleFont } from '@/utils/scaler';
import TopThreeTable from '@/components/features/actor/TopThreeTable';

export default function StockDetails() {
  const { t } = useTranslation();
  const { isin } = useLocalSearchParams<{ isin: string }>();
  const { colors } = useTheme();
  const primaryColor = colors.primary;
  useInitializeActor();

  const {
    data: stockDetails,
    isLoading,
    error,
  } = useQuery<SecurityAccountSecurity>({
    queryKey: ['stock', 'details', isin],
    queryFn: () => apiGet(`/actor/company/${isin}/EUR`),
    enabled: !!isin,
  });

  const { data: dividendData, isLoading: isDividendLoading } = useQuery<CompanyDividendData>({
    queryKey: ['getCompanyDividendsHistory', isin],
    queryFn: () => {
      if (!stockDetails) return Promise.resolve({} as CompanyDividendData);
      return ActorService.getCompanyDividendsHistory({ ...stockDetails, isin }, DividendDisplayOption.ABSOLUTE);
    },
    enabled: !!isin && !!stockDetails,
  });

  if (isLoading) {
    return (
      <View className="flex-1 dark:bg-primary-dark bg-primary-light justify-center items-center">
        <ActivityIndicator size="large" color={primaryColor} />
        <ThemedText className="mt-2 text-gray-600 dark:text-gray-400">{t('common.loading')}</ThemedText>
      </View>
    );
  }

  if (error || !stockDetails) {
    return (
      <View className="flex-1 dark:bg-primary-dark bg-primary-light justify-center items-center">
        <ThemedText className="text-red-500">{t('common.error.failedToLoadStockDetails')}</ThemedText>
      </View>
    );
  }

  const topThreeData = [
    { id: '1', name: 'Dividend 1', value: '0.47' },
    { id: '2', name: 'Dividend 2', value: '0.50' },
    { id: '3', name: 'Dividend 3', value: '0.55' },
  ];

  return (
    <View className="flex-1">
      <ScrollView className="flex-1 px-4 mt-4">
        <Header title={stockDetails?.name ?? ''} />
        <DividendsChart dividendData={dividendData} isLoading={isDividendLoading} />
        <TopThreeTable data={topThreeData} />
        <ThemedCard
          title={t('actor.portfolioSimulator.title')}
          description={t('actor.portfolioSimulator.description')}
          imageSource={require('@/assets/images/insights/rotated.png')}
          imageStyle={{
            height: 119,
            width: 146,
          }}
          biggerImage
        />
        <CompanyHeader isin={isin} name={stockDetails.name} size="small" />
        <CompanyDividendEvolutionWidget security={{ ...stockDetails, isin, type: SecurityAccountSecurityType.STOCK }} />
        <InvestmentChart
          queryKey={range => ['getCompanyPerformanceQuotes', isin, range.toString()]}
          useQuery={useQuery}
          queryFn={range => ActorService.getCompanyQuotes({ ...stockDetails, isin }, range)}
        />
      </ScrollView>
    </View>
  );
}