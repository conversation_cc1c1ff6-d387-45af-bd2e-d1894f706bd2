import React from 'react';

import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';

import ReusableLineChart, { LineDataPoint } from '../features/actor/LineChart/ReusableLineChart';

// Example of how to use the ReusableLineChart component with different data sets

const ExampleCharts: React.FC = () => {
  // Example 1: Simple revenue data
  const revenueData: LineDataPoint[] = [
    { value: 1000 },
    { value: 1200 },
    { value: 1100 },
    { value: 1400 },
    { value: 1600 },
    { value: 1800 },
    { value: 2000 },
  ];

  // Example 2: Temperature data with custom formatting
  const temperatureData: LineDataPoint[] = [
    { value: 20, dataPointText: '20°C' },
    { value: 22, dataPointText: '22°C' },
    { value: 18, dataPointText: '18°C' },
    { value: 25, dataPointText: '25°C' },
    { value: 28, dataPointText: '28°C' },
    { value: 26, dataPointText: '26°C' },
    { value: 24, dataPointText: '24°C' },
  ];

  // Example 3: Stock price data
  const stockData: LineDataPoint[] = [
    { value: 150.2 },
    { value: 152.1 },
    { value: 148.5 },
    { value: 155.3 },
    { value: 158.75 },
    { value: 162.4 },
    { value: 159.8 },
  ];

  const formatTemperature = (value: number): string => `${value}°C`;
  const formatCurrency = (value: number): string => `$${value.toFixed(2)}`;

  return (
    <View style={styles.container}>
      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Revenue Chart
        </ThemedText>
        <ReusableLineChart
          series={[{ data: revenueData, color: '#4CAF50' }]}
          height={120}
          formatValueForPointer={formatCurrency}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Temperature Chart
        </ThemedText>
        <ReusableLineChart
          series={[{ data: temperatureData, color: '#FF9800', areaChart: false, thickness: 3 }]}
          height={120}
          curved={false}
          formatValueForPointer={formatTemperature}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Stock Price Chart
        </ThemedText>
        <ReusableLineChart
          series={[{ data: stockData, color: '#2196F3', startOpacity: 0.3, endOpacity: 0.1 }]}
          height={120}
          formatValueForPointer={formatCurrency}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Simple Line Chart (No Area)
        </ThemedText>
        <ReusableLineChart
          series={[{ data: revenueData, color: '#9C27B0', areaChart: false, thickness: 2, hideDataPoints: false }]}
          height={120}
          showPointer={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  chartSection: {
    marginBottom: 32,
  },
  title: {
    marginBottom: 12,
  },
});

export default ExampleCharts;
