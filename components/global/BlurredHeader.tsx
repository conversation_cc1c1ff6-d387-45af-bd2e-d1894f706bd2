import React from 'react';

import { useSignalEffect } from '@preact/signals-react';
import { BottomTabHeaderProps } from '@react-navigation/bottom-tabs';
import { Icon } from '@rneui/themed';
import { BlurView } from 'expo-blur';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, {
  Easing,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { isHeaderVisible } from '@/signals/app.signal';
import { scaleFont } from '@/utils/scaler';

import PortfolioConnectModal from '../features/portfolio-import/PortfolioConnectModal';
import { ModalManager } from './modal';

type BlurredHeaderProps = BottomTabHeaderProps & { title: string };

const AnimatedBlurView = Animated.createAnimatedComponent(BlurView);

export default function BlurredHeader(props: BlurredHeaderProps) {
  const { colors, isLight } = useTheme();
  const opacity = useSharedValue(0);
  const intensity = useSharedValue(0);
  const color = useSharedValue(isLight ? 'rgba(255, 255, 255, 1)' : 'rgba(0, 0, 0, 1)');

  const shouldBlur = true;

  const config = {
    duration: 300,
    easing: Easing.bezier(0.5, 0.01, 0, 1),
  };

  const style = useAnimatedStyle(() => ({
    opacity: withTiming(opacity.value, config),
  }));

  const animatedProps = useAnimatedProps(() => ({
    intensity: withTiming(intensity.value, config),
  }));

  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: withTiming(color.value, config),
    };
  });

  useSignalEffect(() => {
    opacity.value = isHeaderVisible.value ? 1 : 0;
    intensity.value = isHeaderVisible.value ? 80 : 0;
    color.value = isLight
      ? isHeaderVisible.value
        ? 'rgba(255, 255, 255, 1)'
        : 'rgba(255, 255, 255, 0)'
      : isHeaderVisible.value
        ? 'rgba(0, 0, 0, 1)'
        : 'rgba(0, 0, 0, 0)';
  });

  const RenderContent = () => (
    <View
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        paddingHorizontal: 16,
        paddingTop: 8,
        paddingBottom: 16,
      }}
    >
      <ThemedText size={18} type="outfit-semi-bold" animated style={style}>
        {props.title}
      </ThemedText>

      <View style={{ flexDirection: 'row', gap: 12, position: 'absolute', right: 16, bottom: 12 }}>
        <TouchableOpacity onPress={() => router.push('/main/company')}>
          <Icon
            size={scaleFont(26)}
            name="search"
            type="material"
            className="rounded-full"
            style={{ borderColor: colors.text }}
            color={colors.text}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => ModalManager.showModal(PortfolioConnectModal)}>
          <Icon
            size={scaleFont(28)}
            name="add"
            type="material"
            className="rounded-full"
            style={{ borderColor: colors.text }}
            color={colors.text}
          />
        </TouchableOpacity>
        {/* Do not show profile icon when profile is already open (following temporary solution to add profile in bottom tabs) */}
        {/* {(segments.at(-1) as string) !== 'profile' && (
          <TouchableOpacity onPress={() => router.navigate('/main/settings')}>
            <Avatar
              // Added empty source to remove Image source not found warning (which is a bug from rneui)
              // Look here for more info https://github.com/react-native-elements/react-native-elements/issues/3742#issuecomment-1978783981
              source={{ uri: 'data:image/png' }}
              rounded
              title={profile.email[0].toUpperCase()}
              containerStyle={{ backgroundColor: colors.primary }}
              placeholderStyle={{ backgroundColor: colors.background }}
            />
          </TouchableOpacity>
        )} */}
      </View>
    </View>
  );

  return shouldBlur ? (
    <View style={{ paddingTop: Constants.statusBarHeight }}>
      <AnimatedBlurView
        experimentalBlurMethod="dimezisBlurView"
        tint={isLight ? 'extraLight' : 'dark'}
        animatedProps={animatedProps}
        style={{
          ...StyleSheet.absoluteFillObject,
          overflow: 'hidden',
          backgroundColor: 'transparent',
          alignItems: 'center',
          justifyContent: 'flex-end',
        }}
      />
      <RenderContent />
    </View>
  ) : (
    <Animated.View
      style={[
        animatedStyle,
        {
          ...StyleSheet.absoluteFillObject,
          overflow: 'hidden',
          height: 50,
          alignItems: 'center',
          justifyContent: 'flex-end',
          paddingBottom: 10,
        },
      ]}
    >
      <RenderContent />
    </Animated.View>
  );
}
