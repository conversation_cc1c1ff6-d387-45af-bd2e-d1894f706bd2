import React, { useState } from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import SecurityIcon from '@/components/SecurityIcon';
import { Text } from '@/components/base';
import { useThemeColor } from '@/hooks/useThemeColor';
import { actor } from '@/signals/actor';

import type { TransactionType } from '../../../types/actor-api.types';
import Widget from './Widget';

const getTransactionLabel = (type: TransactionType) => {
  switch (type) {
    case 'PURCHASE':
      return 'buy';
    case 'SALE':
      return 'sell';
    case 'DIVIDEND':
      return 'Dividend';
    default:
      return type;
  }
};

function formatDate(date: string) {
  return new Date(date).toLocaleDateString();
}

const MAX_INITIAL = 6;

export default function ActivitiesWidget() {
  const depot = actor.value.depot;
  const securitiesObj = depot?.securities || {};
  const securities = Object.values(securitiesObj);

  const theme = useThemeColor();
  const { t } = useTranslation();

  // Flatten all transactions with their security reference
  const allTransactions = securities
    .flatMap(security =>
      (security.transactions || []).map(transaction => ({
        ...transaction,
        security,
      })),
    )
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  const [visibleCount, setVisibleCount] = useState(MAX_INITIAL);

  const widgetReady = allTransactions.length > 0;
  const visibleTransactions = allTransactions.slice(0, visibleCount);

  return (
    <Widget title={t('actor.activitiesWidget.title')} ready={widgetReady}>
      {widgetReady ? (
        <>
          {visibleTransactions.map((item, idx) => (
            <View
              key={idx}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingVertical: 16,
                borderBottomWidth: 1,
                borderColor: '#eee',
              }}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                <SecurityIcon
                  isin={item.security.isin}
                  country={item.security.country}
                  style={{ width: 40, height: 40, marginRight: 12 }}
                />
                <View style={{ flexDirection: 'column', justifyContent: 'center' }}>
                  <Text style={{ fontSize: 16, fontWeight: '700', marginBottom: 2 }}>{item.security.name}</Text>
                  <Text style={{ color: theme.muted }}>{formatDate(item.date)}</Text>
                </View>
              </View>
              <View style={{ alignItems: 'flex-end', minWidth: 140 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    marginBottom: 4,
                    color: item.type === 'PURCHASE' ? '#1976D2' : item.type === 'SALE' ? '#D34F2F' : theme.text,
                  }}
                >
                  {t(`actor.activitiesWidget.${getTransactionLabel(item.type)}`)}
                </Text>
                {item.price && (
                  <Text style={{ fontSize: 16, marginBottom: 4 }}>
                    {t('currency', {
                      amount: {
                        amount: item.price.amount,
                        unit: item.price.unit ?? 'EUR',
                        options: {
                          notation: 'compact',
                        },
                      },
                    })}
                  </Text>
                )}
                {item.quantity && item.price && (
                  <Text style={{ color: theme.muted }}>
                    {item.quantity} ×
                    {t('currency', {
                      amount: {
                        amount: item.price.amount / item.quantity,
                        unit: item.price.unit ?? 'EUR',
                        options: {},
                      },
                    })}
                  </Text>
                )}
              </View>
            </View>
          ))}
          {visibleCount < allTransactions.length && (
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 12 }}>
              <Text
                style={{
                  color: '#2E7877',
                  fontWeight: 'bold',
                  padding: 0,
                  borderRadius: 0,
                  fontSize: 16,
                  textAlign: 'right',
                }}
                onPress={() => setVisibleCount(v => v + MAX_INITIAL)}
              >
                {t('actor.activitiesWidget.more')}
              </Text>
            </View>
          )}
        </>
      ) : (
        <View className="flex-1 justify-center items-center" style={{ height: 225 }}>
          <Text className="text-gray-500 text-lg">{t('actor.activitiesWidget.noData')}</Text>
        </View>
      )}
    </Widget>
  );
}
