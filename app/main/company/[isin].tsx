import React from 'react';

import { useQuery } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, ScrollView, View } from 'react-native';

import { apiGet } from '@/common/api';
import MetricsWidgetExample from '@/components/examples/MetricsWidgetExample';
import {
  CompanyDividendEvolutionWidget,
  CompanyDividendYieldWidget,
  CompanyIsinWknWidget,
  CompanySectorsWidget,
  CompanySharePriceWidget,
} from '@/components/features/actor';
import ExpandableAssetTable from '@/components/features/actor/AssetTable/ExpandableAssetTable';
import { allAssets, topThreeAssets } from '@/components/features/actor/AssetTable/mockAssetTableData';
import Company52WeekRangeWidget from '@/components/features/actor/Company52WeekRangeWidget';
import CompanyHeader from '@/components/features/actor/CompanyHeader';
import CurrentPriceWidget from '@/components/features/actor/CurrentPriceWidget';
import DividendsChart from '@/components/features/actor/DividendsChart';
import QuotesChart from '@/components/features/actor/QuotesChart';
import { Header } from '@/components/global/Header';
import { ThemedCard } from '@/components/global/ThemedCard';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import useInitializeActor from '@/hooks/useInitializeActor';
import { ActorService } from '@/services/actor.service';
import { CompanyDividendData, DividendDisplayOption, SecurityAccountSecurity } from '@/types/actor-api.types';
import { SecurityAccountSecurityType } from '@/types/secapi.types';

export default function StockDetails() {
  const { t } = useTranslation();
  const { isin } = useLocalSearchParams<{ isin: string }>();
  const { colors } = useTheme();
  const primaryColor = colors.primary;
  useInitializeActor();

  const {
    data: stockDetails,
    isLoading,
    error,
  } = useQuery<SecurityAccountSecurity>({
    queryKey: ['stock', 'details', isin],
    queryFn: () => apiGet(`/actor/company/${isin}/EUR`),
    enabled: !!isin,
  });

  // Query for dividend data to pass to DividendsChart
  const { data: dividendData, isLoading: isDividendLoading } = useQuery<CompanyDividendData>({
    queryKey: ['getCompanyDividendsHistory', isin],
    queryFn: () => {
      if (!stockDetails) return Promise.resolve({} as CompanyDividendData);
      return ActorService.getCompanyDividendsHistory({ ...stockDetails, isin }, DividendDisplayOption.ABSOLUTE);
    },
    enabled: !!isin && !!stockDetails,
  });

  if (isLoading) {
    return (
      <View className="flex-1 dark:bg-primary-dark bg-primary-light justify-center items-center">
        <ActivityIndicator size="large" color={primaryColor} />
        <ThemedText className="mt-2 text-gray-600 dark:text-gray-400">{t('common.loading')}</ThemedText>
      </View>
    );
  }

  if (error || !stockDetails) {
    return (
      <View className="flex-1 dark:bg-primary-dark bg-primary-light justify-center items-center">
        <ThemedText className="text-red-500">{t('common.error.failedToLoadStockDetails')}</ThemedText>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <ScrollView className="flex-1 px-4 mt-4">
        <Header title={stockDetails?.name ?? ''} />

        <CurrentPriceWidget security={{ ...stockDetails, isin }} />
        <CompanyDividendYieldWidget security={{ ...stockDetails, isin, type: SecurityAccountSecurityType.STOCK }} />
        <Company52WeekRangeWidget security={{ ...stockDetails, isin, type: SecurityAccountSecurityType.STOCK }} />
        <DividendsChart dividendData={dividendData} isLoading={isDividendLoading} />
        <ThemedCard
          title={t('actor.portfolioSimulator.title')}
          description={t('actor.portfolioSimulator.description')}
          imageSource={require('@/assets/images/insights/rotated.png')}
          imageStyle={{
            height: 119,
            width: 146,
          }}
          biggerImage
        />
        <CompanyHeader isin={isin} name={stockDetails.name} size="small" />
        {/* Top Three Assets Table */}
        <QuotesChart
          title={t('actor.investment.title')}
          queryKey={range => ['getCompanyPerformanceQuotes', isin, range.toString()]}
          useQuery={useQuery}
          queryFn={range => ActorService.getCompanyQuotes({ ...stockDetails, isin }, range)}
          noDataText={t('actor.performanceChart.noData')}
        />
        {/* All Assets Table */}
        {/*<CompanyDividendHistoryWidget
          queryKey={() => ['getCompanyDividendsHistory', isin]}
          queryFn={() =>
            ActorService.getCompanyDividendsHistory({ ...stockDetails, isin }, DividendDisplayOption.ABSOLUTE)
          }
        />

        <CompanyQuotesWidget
          queryKey={range => ['getCompanyPerformanceQuotes', isin, range.toString()]}
          useQuery={useQuery}
          queryFn={range => ActorService.getCompanyQuotes({ ...stockDetails, isin }, range)}
        />

       
        
         */}
        <CompanyDividendEvolutionWidget security={{ ...stockDetails, isin, type: SecurityAccountSecurityType.STOCK }} />
        {/*    <CompanyDividendTableWidget security={{ ...stockDetails, isin }} />
      
        <CompanySectorsWidget security={{ ...stockDetails, isin }} />*/}

        <MetricsWidgetExample />
        <CompanySectorsWidget security={{ ...stockDetails, isin }} />
      </ScrollView>
    </View>
  );
}
