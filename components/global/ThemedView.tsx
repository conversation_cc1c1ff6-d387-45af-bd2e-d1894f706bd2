import { LinearGradient } from 'expo-linear-gradient';
import { View, type ViewProps } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  useGradient?: boolean; // New prop to control gradient usage
};

export function ThemedView({ style, lightColor, darkColor, useGradient = true, ...otherProps }: ThemedViewProps) {
  const { colors, isLight } = useTheme();

  // Handle custom light/dark colors or use theme background
  const backgroundColor = isLight ? lightColor || colors.background : darkColor || colors.background;
  const gradientColors = colors.linearGradient;

  if (useGradient) {
    return (
      <LinearGradient
        colors={gradientColors}
        style={[style, { backgroundColor }]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        {...otherProps}
      >
        {otherProps.children}
      </LinearGradient>
    );
  }

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
