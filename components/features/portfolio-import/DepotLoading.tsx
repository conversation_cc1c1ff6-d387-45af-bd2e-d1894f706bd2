import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';
import { Circle } from 'react-native-progress';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { portfolioConnect } from '@/signals/portfolio-connect';

export function DepotLoading() {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();

  const progress = portfolioConnect.value.secapiImport.progress;

  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <Circle
          progress={progress}
          size={200}
          thickness={6}
          color={colors.icon}
          showsText={true}
          textStyle={styles.progressText}
        />
        <ThemedText size={26} type="bold" style={styles.text}>
          {progress < 1.0 ? t('portfolioConnect.loadingDepot') : t('portfolioConnect.loadingDepotSuccess')}
        </ThemedText>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  innerContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  text: {
    marginTop: 40,
    textAlign: 'center',
  },
  progressText: {
    fontSize: 28,
  },
});
