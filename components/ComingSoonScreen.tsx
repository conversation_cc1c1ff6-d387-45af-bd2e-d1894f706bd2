import React from 'react';

import { Icon } from '@rneui/themed';
import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';

import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

import { Text } from './base';
import { SafeAreaView } from './base/SafeAreaView';

interface ComingSoonScreenProps {
  iconName: string;
}

export default function ComingSoonScreen({ iconName }: ComingSoonScreenProps) {
  const { t } = useTranslation();
  const { colors } = useTheme();

  return (
    <SafeAreaView style={styles.container}>
      <Icon name={iconName} type="material" size={scaleFont(60)} color={colors.primary} style={styles.icon} />
      <Text h3 style={[styles.title, { color: colors.primary }]}>
        {t('comingSoon.title')}
      </Text>
      <Text style={styles.stayTuned}>{t('comingSoon.stayTuned')}</Text>
      <Text style={styles.emojis}>🚀💵🕉️🌅</Text>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontWeight: '900',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    maxWidth: '80%',
    marginBottom: 10,
  },
  stayTuned: {
    fontSize: 16,
    textAlign: 'center',
    maxWidth: '80%',
  },
  emojis: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});
