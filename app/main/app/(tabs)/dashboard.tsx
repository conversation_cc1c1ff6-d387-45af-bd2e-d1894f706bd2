import { useSignals } from '@preact/signals-react/runtime';
import { useTranslation } from 'react-i18next';
import { LogBox } from 'react-native';
import { RefreshControl } from 'react-native-gesture-handler';

import { ScrollScreen } from '@/components/base';
import { CalendarWidget, PortfolioSelector } from '@/components/features/actor';
import ExpandableAssetTable from '@/components/features/actor/AssetTable/ExpandableAssetTable';
import { allAssets, topThreeAssets } from '@/components/features/actor/AssetTable/mockAssetTableData';
import FavoritesWidget from '@/components/features/actor/FavoritesWidget';
import InsightsWidget from '@/components/features/actor/InsightsWidget';
import MarketStatus from '@/components/features/actor/MarketStatus';
import PerformanceWidget from '@/components/features/actor/PerformanceWidget';
import QuotesChart from '@/components/features/actor/QuotesChart';
import ValueDivisionWidget from '@/components/features/actor/ValueDivisionWidget';
import YieldTable from '@/components/features/actor/YieldTable';
import { ThemedText } from '@/components/global/ThemedText';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import useInitializeActor from '@/hooks/useInitializeActor';
import { ActorService } from '@/services/actor.service';

LogBox.ignoreLogs(['Image source "null" doesn\'t exist', 'No stops in gradient']);

export default function Analyze() {
  const { t } = useTranslation();
  const { isFetching, refetch } = useInitializeActor();

  useSignals();

  return (
    <>
      <ScrollScreen
        refreshControl={
          <RefreshControl
            refreshing={isFetching}
            onRefresh={() => {
              refetch();
            }}
          />
        }
      >
        <ThemedText size={24} type="outfit-semi-bold" className="mb-5 mx-1.5">
          {t('common.tabs.dashboard')}
        </ThemedText>

        <PortfolioSelector style={{ marginBottom: 24 }} />
        <InsightsWidget />
        <MarketStatus />
        <FavoritesWidget />
        {/* <PortfolioStatsWidget /> */}

        <PerformanceWidget
          onClaimPress={() => {
            // TODO: Implement claim functionality
            console.log('Claim button pressed');
          }}
        />
        <YieldTable />
        <ExpandableAssetTable title="Top three" data={topThreeAssets} collapsedCount={3} />

        <ExpandableAssetTable title="All assets" data={allAssets} collapsedCount={3} isTopAsset />

        <QuotesChart
          title={t('actor.performanceChart.title')}
          queryKey={range => ['getPerformanceQuotes', range.toString()]}
          useQuery={usePortfolioQuery}
          queryFn={range => ActorService.getPerformanceQuotes(range)}
          noDataText={t('actor.performanceChart.noData')}
        />

        {/**  <CompanyQuotesWidget
          queryKey={range => ['getPerformanceQuotes', range.toString()]}
          useQuery={usePortfolioQuery}
          queryFn={range => ActorService.getPerformanceQuotes(range)}
          enableTWROR
        /> */}
        <ValueDivisionWidget />
        <CalendarWidget />

        {/*<DividendsChart />

        <DivisionWidget />

        <SimulationWidget />
        <AssetClassesWidget />*/}
      </ScrollScreen>
    </>
  );
}
