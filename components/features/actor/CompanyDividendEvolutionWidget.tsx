import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { SecurityAccountSecurity } from '@/types/secapi.types';

import { ActorService } from '../../../services/actor.service';
import Widget from './Widget';

export type CompanyDividendEvolutionWidgetProps = {
  security: SecurityAccountSecurity;
};

export default function CompanyDividendEvolutionWidget({ security }: CompanyDividendEvolutionWidgetProps) {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();

  const continuousIncreasesYears = React.useMemo(
    () => (security ? ActorService.getContinuousDividendIncreases(security) : undefined),
    [security],
  );

  const noDividendsCuts = React.useMemo(
    () => (security ? ActorService.getYearsWithNoDividendCuts(security) : undefined),
    [security],
  );

  const getCurrentYear = () => {
    return new Date().getFullYear();
  };

  const formatIntervals = (intervals?: number[][]) => {
    if (!intervals || intervals.length === 0) return null;

    const formattedIntervals = intervals.map(interval => {
      const [start, end] = interval;

      if (end === getCurrentYear()) {
        return t('actor.dividendEvolutionWidget.since') + ` ${start}`;
      } else if (start === end) {
        return `${start}`;
      } else {
        return `${start}-${end}`;
      }
    });
    return (
      <View className="flex-row flex-wrap">
        {formattedIntervals.map((interval, index) => (
          <ThemedText key={index} type="semi-bold" style={{ color: colors.primary, marginTop: 5 }}>
            {interval}
            {index < formattedIntervals.length - 1 && <ThemedText className="text-gray-600"> • </ThemedText>}
          </ThemedText>
        ))}
      </View>
    );
  };

  return (
    <Widget title={t('actor.dividendEvolutionWidget.title')} ready>
      {continuousIncreasesYears || noDividendsCuts ? (
        <View className="flex flex-col gap-4 mt-3 mb-3 py-5">
          {continuousIncreasesYears && (
            <ExpoLinearGradient
              colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ paddingVertical: 5, paddingHorizontal: 16 }}
            >
              <ThemedText size={14}>{t('actor.dividendEvolutionWidget.continuousIncreasesYears')}</ThemedText>
              <View>{formatIntervals(continuousIncreasesYears)}</View>
            </ExpoLinearGradient>
          )}

          {noDividendsCuts && (
            <View style={{ paddingVertical: 5, paddingHorizontal: 16 }}>
              <ThemedText size={14}>{t('actor.dividendEvolutionWidget.noDividendsCuts')}</ThemedText>
              <View>{formatIntervals(noDividendsCuts)}</View>
            </View>
          )}
        </View>
      ) : (
        <View className="flex-1 justify-center items-center p-5">
          <ThemedText size={14}>{t('actor.dividendEvolutionWidget.noDividendPayments')}</ThemedText>
        </View>
      )}
    </Widget>
  );
}
