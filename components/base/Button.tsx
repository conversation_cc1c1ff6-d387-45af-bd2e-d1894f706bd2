import React from 'react';

import { ButtonProps, Button as NativeButton } from '@rneui/themed';
import { StyleSheet } from 'react-native';

import { useTheme } from '@/context/ThemeContext';
import '@/global.css';

export interface StyledButtonProps extends ButtonProps {
  variant?: 'primary' | 'secondary';
}

export const Button: React.FC<StyledButtonProps> = ({
  loading,
  onPress,
  variant = 'primary',
  disabledStyle,
  containerStyle,
  disabledTitleStyle,
  ...rest
}) => {
  const { colors, isLight, isDark } = useTheme();

  const primaryColor = colors.primary;

  const buttonStyle = [
    styles.button,
    variant === 'secondary' && styles.secondaryButton,
    variant === 'primary'
      ? {
          backgroundColor: primaryColor,
          borderColor: primaryColor,
          borderWidth: 1,
          borderRadius: 12,
        }
      : {
          borderColor: primaryColor,
        },
    rest.buttonStyle,
  ];

  const titleStyle = [
    styles.buttonText,
    variant === 'primary' && {
      color: colors.background, // Use theme background color for primary button text
    },
    variant === 'secondary' && {
      color: primaryColor,
    },
    rest.titleStyle,
  ];

  return (
    <NativeButton
      disabledStyle={[styles.disabledButton, { backgroundColor: colors.tabIconDefault }, disabledStyle]}
      disabledTitleStyle={[styles.disabledText, { color: colors.background }, disabledTitleStyle]}
      buttonStyle={buttonStyle}
      containerStyle={[styles.buttonContainer, containerStyle]}
      titleStyle={titleStyle}
      loading={loading}
      onPress={loading ? undefined : onPress}
      {...rest}
    />
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderRadius: 12,
  },
  disabledButton: {
    // Will be overridden with theme colors
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  disabledText: {
    // Will be overridden with theme colors
  },
});
