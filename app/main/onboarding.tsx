import React, { useRef, useState } from 'react';

import R<PERSON>ateTimePicker, { DateTimePickerAndroid, DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { Icon } from '@rneui/base';
import { Button } from '@rneui/themed';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import i18next from 'i18next';
import { useTranslation } from 'react-i18next';
import { Image } from 'react-native';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';

import { apiPost } from '@/common/api';
import { countries } from '@/common/countries';
import { usePrincipalLegalEntity, useUserProfile } from '@/common/profile';
import { SafeAreaView } from '@/components/base';
import { useSnackbar } from '@/components/global/Snackbar';
import { ThemedText } from '@/components/global/ThemedText';
import { OnboardingCard } from '@/components/onboarding/OnboardingCard';
import { OnboardingHero } from '@/components/onboarding/OnboardingHero';
import { getThemeColors } from '@/constants/Colors';
import { ColorScheme } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { usePurchases } from '@/hooks/usePurchases';
import { useSafeAreaHeight } from '@/hooks/useSafeAreaHeight';
import { ThemeType } from '@/types/theme';

import { OnboardingPageHeader } from '../../components/onboarding/OnboardingPageHeader';
import { supportedLanguages } from './settings';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

enum OnboardingPage {
  INTRO,
  THEME_SELECT,
  THEME_COLOR_SELECT,
  LANGUAGE_SELECT,
  AI_DISCLAIMER,
  TAX_RESIDENCY,
  BIRTHDAY,
  FREE_TRIAL,
  ALL_SET,
}

export default function OnboardingModal() {
  const { t } = useTranslation();
  const { theme, setTheme, currentTheme, colors, isLight } = useTheme();
  const styles = createStyles(colors);
  const { updateProfile, updatePrincipalLegalEntity, profile } = useUserProfile();
  const principalLegalEntity = usePrincipalLegalEntity();
  const [currentPage, setCurrentPage] = useState<OnboardingPage>(OnboardingPage.THEME_SELECT);
  const [selectedTheme, setSelectedTheme] = useState<'system' | 'lightBlue' | 'darkBlue'>(
    theme === 'system' || theme === 'lightBlue' || theme === 'darkBlue' ? theme : 'system',
  );
  // For color selection page
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState(profile.flags?.language || 'en-US');
  const [selectedCountry, setSelectedCountry] = useState(principalLegalEntity?.data.info.nationality ?? '');
  const [birthday, setBirthday] = useState<Date | null>(
    principalLegalEntity?.data.info.birthday ? new Date(principalLegalEntity.data.info.birthday) : null,
  );
  const [skippedBirthday, setSkippedBirthday] = useState(false);

  const { refreshCustomerInfo } = usePurchases();
  const { showSnackbar } = useSnackbar();

  const onDateChange = (event: DateTimePickerEvent, selectedDate?: Date | undefined) => {
    if (event.type === 'set') {
      // Only update if a date is selected
      const currentDate = selectedDate || birthday;
      setBirthday(currentDate);
    }
  };

  const scrollViewRef = useRef<ScrollView>(null);
  const [isLoading, setIsLoading] = useState(false);

  const prioritizedCountryIds = ['de', 'ch', 'at', 'li'];
  const prioritizedCountries = [
    ...prioritizedCountryIds.map(id => countries.find(country => country.id === id)!),
    ...countries.filter(country => !prioritizedCountryIds.includes(country.id)),
  ];

  const renderHero = (showBackButton = true, showImages = true) => (
    <OnboardingHero
      isLight={isLight}
      showBackButton={showBackButton}
      showImages={showImages}
      onBack={() => {
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            x: screenWidth * (currentPage - 1),
            animated: true,
          });
        }, 100);
      }}
    />
  );

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const page = Math.round(contentOffsetX / screenWidth);
    setCurrentPage(page as OnboardingPage);
  };

  let availablePages = 6;
  if (principalLegalEntity?.data.info.nationality) {
    availablePages = 7;
    if (principalLegalEntity?.data.info.birthday || skippedBirthday) {
      availablePages = 8;
      if (!profile.flags.usedCompanionTrial) {
        availablePages = 9;
      }
    }
  }

  const formatDate = (date: Date) => date.toISOString().slice(0, 10);

  // Page indicator removed

  const renderPages = () => {
    // Theme color options from ThemeContext/Colors
    const lightThemes = [
      { key: 'lightBlue', name: t('Balanced blue') },
      { key: 'lightOrange', name: t('Sunny orange') },
      { key: 'lightGreen', name: t('Fresh green') },
      { key: 'lightPurple', name: t('Vivid purple') },
      { key: 'lightRed', name: t('Vivid red') },
    ];
    const darkThemes = [
      { key: 'darkBlue', name: t('Midnight blue') },
      { key: 'darkOrange', name: t('Deep orange') },
      { key: 'darkGreen', name: t('Electric green') },
      { key: 'darkPurple', name: t('Deep purple') },
      { key: 'darkRed', name: t('Neon red') },
    ];
    const themeType =
      selectedTheme === 'lightBlue' ? 'light' : selectedTheme === 'darkBlue' ? 'dark' : isLight ? 'light' : 'dark';
    const themeList = themeType === 'light' ? lightThemes : darkThemes;

    // Helper for next button with disabled logic for specific pages and correct persistence
    const renderNextButton = (pageIdx: number, extraProps = {}) => {
      let disabled = false;
      if (pageIdx === OnboardingPage.TAX_RESIDENCY) {
        disabled = !selectedCountry;
      } else if (pageIdx === OnboardingPage.BIRTHDAY) {
        disabled = !birthday || !isValidBirthday(birthday);
      } else if (pageIdx === OnboardingPage.FREE_TRIAL && !profile.flags.usedCompanionTrial) {
        disabled = isLoading;
      }
      const handleNext = async () => {
        try {
          if (pageIdx === OnboardingPage.LANGUAGE_SELECT) {
            setIsLoading(true);
            await apiPost('/users/language', { language: selectedLanguage });
            updateProfile(p => {
              p.flags.language = selectedLanguage;
            });
          } else if (pageIdx === OnboardingPage.TAX_RESIDENCY) {
            setIsLoading(true);
            await apiPost(`/legalEntities/${principalLegalEntity?.id}/nationality`, { nationality: selectedCountry });
            updatePrincipalLegalEntity(p => {
              p.data.info.nationality = selectedCountry;
            });
          } else if (pageIdx === OnboardingPage.BIRTHDAY) {
            setIsLoading(true);
            const formattedBirthday = formatDate(birthday!);
            await apiPost(`/legalEntities/${principalLegalEntity?.id}/birthday`, { birthday: formattedBirthday });
            updatePrincipalLegalEntity(p => {
              p.data.info.birthday = formattedBirthday;
            });
          } else if (pageIdx === OnboardingPage.FREE_TRIAL && !profile.flags.usedCompanionTrial) {
            setIsLoading(true);
            await apiPost('/activate-trial', {});
            await refreshCustomerInfo();
          }
          setTimeout(() => {
            scrollViewRef.current?.scrollTo({
              x: screenWidth * (pageIdx + 1),
              animated: true,
            });
          }, 200);
        } catch (error: any) {
          showSnackbar(error.message, { type: 'error' });
        } finally {
          setIsLoading(false);
        }
      };
      return (
        <View style={{ alignItems: 'center', marginBottom: 24 }}>
          <Button
            title={t('common.next')}
            buttonStyle={styles.modalButton}
            titleStyle={styles.buttonText}
            onPress={handleNext}
            disabled={disabled || isLoading}
            loading={isLoading}
            {...extraProps}
          />
        </View>
      );
    };

    return (
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEnabled={false}
        scrollEventThrottle={16}
      >
        {/* INTRO PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.page}>
            {renderHero(false, true)}
            <View>
              <ThemedText size={32} type="bold" style={styles.modalTitle}>
                {t('onboarding.intro.title')}
              </ThemedText>
              <ThemedText size={18} style={styles.modalText}>
                {t('onboarding.intro.message')}
              </ThemedText>
            </View>
            {renderNextButton(OnboardingPage.INTRO)}
          </View>
        </ScrollView>

        {/* THEME SELECTION PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={[styles.page]}>
            {renderHero(true, true)}
            <View style={{ flexGrow: 1, alignItems: 'center', justifyContent: 'flex-start' }}>
              <ExpoLinearGradient
                colors={isLight ? ['#1111110A', '#1111110A'] : ['#FFFFFF0A', '#FFFFFF0A']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.themeTabsBg}
              >
                <View style={[styles.themeTabsRow, { zIndex: 20 }]}>
                  {['lightBlue', 'darkBlue', 'system'].map(option => (
                    <TouchableOpacity
                      key={option}
                      style={styles.themeTab}
                      onPress={() => {
                        setSelectedTheme(option as 'lightBlue' | 'darkBlue' | 'system');
                        setTheme(option as 'lightBlue' | 'darkBlue' | 'system');
                      }}
                      activeOpacity={0.7}
                    >
                      <View style={styles.themeTabInner}>
                        <ThemedText
                          type="outfit-semi-bold"
                          size={28}
                          style={[styles.themeTabText, selectedTheme === option && styles.themeTabTextSelected]}
                        >
                          {option === 'lightBlue' ? t('Light') : option === 'darkBlue' ? t('Dark') : t('System')}
                        </ThemedText>
                        {selectedTheme === option && <View style={styles.themeTabUnderline} />}
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
              </ExpoLinearGradient>
              <View style={{ width: '100%', alignItems: 'center', position: 'relative' }}>
                <OnboardingCard
                  buttonLabel={t('common.next')}
                  onButtonPress={() => {
                    setTimeout(() => {
                      scrollViewRef.current?.scrollTo({
                        x: screenWidth * (currentPage + 1),
                        animated: true,
                      });
                    }, 200);
                  }}
                >
                  <ThemedText size={12} style={{ textAlign: 'center', marginBottom: 4 }}>
                    {t('Great choice. Now select your display mode: light or dark.')}
                  </ThemedText>
                </OnboardingCard>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* THEME COLOR SELECTION PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={[styles.page, { flexDirection: 'column', alignItems: 'center' }]}>
            {renderHero(true, true)}
            <View style={{ flexGrow: 1, alignItems: 'center', justifyContent: 'flex-start', width: '100%' }}>
              <ThemedText size={28} type="outfit-semi-bold" style={{ textAlign: 'center', marginBottom: 24 }}>
                {themeList.find(t => t.key === currentTheme)?.name || themeList[0].name}
              </ThemedText>
              <OnboardingCard
                buttonLabel={t('common.next')}
                onButtonPress={() => {
                  setTimeout(() => {
                    scrollViewRef.current?.scrollTo({
                      x: screenWidth * (currentPage + 1),
                      animated: true,
                    });
                  }, 200);
                }}
              >
                <ThemedText size={12} style={{ textAlign: 'center', marginBottom: 16 }}>
                  {t("Let's start by picking your dashboard theme")}
                </ThemedText>
                <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 8 }}>
                  {themeList.map((theme, idx) => {
                    const isSelected = currentTheme === theme.key;
                    const appThemeKeys = [
                      'lightOrange',
                      'lightRed',
                      'lightPurple',
                      'lightBlue',
                      'lightGreen',
                      'darkOrange',
                      'darkRed',
                      'darkPurple',
                      'darkBlue',
                      'darkGreen',
                    ];
                    const themeColors = appThemeKeys.includes(theme.key) ? getThemeColors(theme.key as any) : colors;
                    const size = isSelected ? 32 * 1.1 : 32;
                    return (
                      <TouchableOpacity
                        key={theme.key}
                        style={{
                          width: size,
                          height: size,
                          borderRadius: size / 2,
                          backgroundColor: themeColors.primary,
                          marginHorizontal: 8,
                          borderWidth: isSelected ? 3 : 1,
                          borderColor: isSelected ? colors.secondary : '#ccc',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                        onPress={() => {
                          setTheme(theme.key as ThemeType);
                        }}
                      />
                    );
                  })}
                </View>
              </OnboardingCard>
            </View>
          </View>
        </ScrollView>

        {/* LANGUAGE SELECT PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.page}>
            {renderHero(true, false)}
            <OnboardingPageHeader
              title={t('onboarding.languageSelect.title')}
              description={t('onboarding.languageSelect.message')}
            />

            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedLanguage}
                onValueChange={newLang => setSelectedLanguage(newLang)}
                style={styles.picker}
                itemStyle={{ color: colors.text, backgroundColor: colors.inputBackground }}
                dropdownIconColor={colors.text}
              >
                {supportedLanguages.map(lang => (
                  <Picker.Item
                    key={lang}
                    label={t(`language.${lang.split('-')[0]}`)}
                    value={lang}
                    color={colors.text}
                  />
                ))}
              </Picker>
            </View>
            {renderNextButton(OnboardingPage.LANGUAGE_SELECT)}
          </View>
        </ScrollView>

        {/* AI DISCLAIMER PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.page}>
            {renderHero(true, true)}
            <OnboardingPageHeader
              title={t('onboarding.aiDisclaimer.title')}
              description={t('onboarding.aiDisclaimer.message')}
            />
            {renderNextButton(OnboardingPage.AI_DISCLAIMER)}
          </View>
        </ScrollView>

        {/* TAX RESIDENCY PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.page}>
            {renderHero(true, false)}
            <OnboardingPageHeader
              title={t('onboarding.taxResidency.title')}
              description={t('onboarding.taxResidency.message')}
            />
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedCountry}
                onValueChange={itemValue => setSelectedCountry(itemValue)}
                style={styles.picker}
                itemStyle={{ color: colors.text, backgroundColor: colors.inputBackground }}
                dropdownIconColor={colors.text}
              >
                <Picker.Item label="" value="" color={colors.text} />
                {prioritizedCountries.map(country => (
                  <Picker.Item key={country.id} label={country.name as string} value={country.id} color={colors.text} />
                ))}
              </Picker>
            </View>
            {renderNextButton(OnboardingPage.TAX_RESIDENCY)}
          </View>
        </ScrollView>

        {/* BIRTHDAY PAGE */}
        <ScrollView style={{ flex: 1, width: screenWidth }} contentContainerStyle={{ flexGrow: 1 }}>
          <View style={styles.page}>
            {renderHero(true, false)}
            <OnboardingPageHeader
              title={t('onboarding.birthday.title')}
              description={t('onboarding.birthday.message')}
            />
            <View>
              {Platform.OS === 'android' ? (
                <View>
                  <TouchableOpacity
                    onPress={() =>
                      DateTimePickerAndroid.open({ value: birthday || new Date(), onChange: onDateChange })
                    }
                    className="px-5 py-4 bg-secondary-light rounded-xl flex flex-row justify-between items-center w-[100%]"
                  >
                    <ThemedText size={18} className="text-slate-800">
                      {!!birthday ? t('{{date,day}}', { date: birthday }) : ''}
                    </ThemedText>
                    <Icon name="event" color={colors.primary} type="material" />
                  </TouchableOpacity>
                </View>
              ) : (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                  }}
                >
                  <RNDateTimePicker
                    value={birthday || new Date()}
                    onChange={onDateChange}
                    locale={i18next.language}
                    mode="date"
                    display="default"
                    themeVariant={isLight ? 'light' : 'dark'}
                  />
                  <TouchableOpacity
                    onPress={async () => {
                      setSkippedBirthday(true);
                      setIsLoading(true);
                      try {
                        setTimeout(() => {
                          scrollViewRef.current?.scrollTo({
                            x: screenWidth * (currentPage + 1),
                            animated: true,
                          });
                        }, 200);
                      } finally {
                        setIsLoading(false);
                      }
                    }}
                  >
                    <ThemedText size={14} textType="muted" className="mt-4">
                      {t('onboarding.birthday.skip')}
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              )}
            </View>
            {renderNextButton(OnboardingPage.BIRTHDAY)}
          </View>
        </ScrollView>

        {/* FREE TRIAL PAGE */}
        {!profile.flags.usedCompanionTrial && (
          <View style={styles.page}>
            {renderHero(true, true)}
            <OnboardingPageHeader
              title={t('onboarding.freeTrial.title')}
              subtitle={t(`subscription.trialPeriod.free`)}
              description={t(`subscription.trialPeriod.after`)}
            />
            <View style={{ alignItems: 'center', marginBottom: 24 }}>
              <Button
                title={isLoading ? t('common.loading') : t('onboarding.freeTrial.startTrial')}
                buttonStyle={styles.modalButton}
                titleStyle={styles.buttonText}
                loading={isLoading}
                disabled={isLoading}
                onPress={async () => {
                  try {
                    setIsLoading(true);
                    await apiPost('/activate-trial', {});
                    await refreshCustomerInfo();
                    setTimeout(() => {
                      scrollViewRef.current?.scrollTo({
                        x: screenWidth * (OnboardingPage.FREE_TRIAL + 1),
                        animated: true,
                      });
                    }, 200);
                  } catch (error: any) {
                    showSnackbar(error.message, { type: 'error' });
                  } finally {
                    setIsLoading(false);
                  }
                }}
              />
            </View>
          </View>
        )}

        {/* ALL SET PAGE */}
        {availablePages >= 6 && (
          <View style={styles.page}>
            {renderHero(true, true)}
            <OnboardingPageHeader title={t('onboarding.allSet.title')} description={t('onboarding.allSet.message')} />
            <View style={{ alignItems: 'center', marginBottom: 24 }}>
              <Button
                title={isLoading ? t('common.loading') : t('onboarding.finalConfirm')}
                buttonStyle={styles.modalButton}
                titleStyle={styles.buttonText}
                loading={isLoading}
                disabled={isLoading}
                onPress={async () => {
                  try {
                    setIsLoading(true);
                    await apiPost('/users/flag', {
                      name: 'allowedCompanionAI',
                      value: true,
                    });
                    updateProfile(p => {
                      p.flags.allowedCompanionAI = true;
                    });
                    router.navigate('/main/app');
                  } catch (error: any) {
                    showSnackbar(error.message, { type: 'error' });
                  } finally {
                    setIsLoading(false);
                  }
                }}
              />
            </View>
          </View>
        )}
      </ScrollView>
    );
  };

  const isValidBirthday = (date: Date | null) => {
    if (!date) return false;
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    return +date < +oneYearAgo;
  };

  return (
    <>
      <StatusBar barStyle={isLight ? 'dark-content' : 'light-content'} />
      <SafeAreaView style={[styles.safeArea]} noMarginBottom>
        <View style={styles.modalContainer}>{renderPages()}</View>
      </SafeAreaView>
    </>
  );
}

const createStyles = (colors: ColorScheme) =>
  StyleSheet.create({
    safeArea: {
      flex: 1,
    },
    gradientBackground: {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      height: screenHeight,
    },
    modalContainer: {
      flex: 1,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    page: {
      flex: 1,
      width: screenWidth,
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    modalTitle: {
      textAlign: 'center',
    },
    modalText: {
      textAlign: 'center',
    },
    modalButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 30,
      paddingVertical: 15,
      borderRadius: 30,
      minWidth: 200,
    },
    buttonText: {
      fontSize: 18,
      fontWeight: '600',
      color: 'white',
    },
    pageIndicator: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
    },
    indicatorDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: 'rgba(0, 0, 0, 0.2)',
      marginHorizontal: 5,
    },
    indicatorDotActive: {
      backgroundColor: colors.primary,
    },
    dropdown: {
      width: '80%',
      marginBottom: 20,
      backgroundColor: colors.inputBackground,
      borderRadius: 8,
      padding: 12,
    },
    pickerContainer: {
      width: '90%',
      alignSelf: 'center',
      marginBottom: 20,
      backgroundColor: colors.inputBackground,
      borderRadius: 8,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    picker: {
      width: '100%',
      color: colors.text,
      backgroundColor: colors.inputBackground,
    },
    dateInput: {
      width: 200,
      height: 40,
      borderColor: colors.border,
      borderWidth: 1,
      borderRadius: 5,
      paddingHorizontal: 10,
      backgroundColor: colors.inputBackground,
      color: colors.text,
    },
    logo: {
      width: 183,
      height: 24,
      marginBottom: 16,
      alignSelf: 'center',
    },
    bubble: {
      width: 346,
      height: 264,
      marginTop: 18,
      alignSelf: 'center',
      marginBottom: 12,
    },
    themeTabsBg: {
      marginBottom: 36,
      alignSelf: 'center',
      paddingVertical: 8,
      width: '100%',
    },
    themeTabsRow: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    themeTab: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 10,
      minWidth: 70,
      position: 'relative',
    },
    themeTabInner: {
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
    },
    themeTabText: {
      fontWeight: '500',
      color: colors.text,
      fontSize: 20,
    },
    themeTabTextSelected: {
      fontWeight: '700',
    },
    themeTabUnderline: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: -6,
      height: 3,
      width: 36,
      backgroundColor: colors.text,
      borderRadius: 2,
      alignSelf: 'center',
    },
  });
