/**
 * Converts a hex color string to an HSL object
 * @param hex - hex color string (e.g. #RRGGBB)
 * @returns hsl Object
 */
export const hexToHsl = (hex: string) => {
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    r = parseInt(hex.slice(1, 3), 16);
    g = parseInt(hex.slice(3, 5), 16);
    b = parseInt(hex.slice(5, 7), 16);
  }
  r /= 255;
  g /= 255;
  b /= 255;
  const max = Math.max(r, g, b),
    min = Math.min(r, g, b);
  let h = 0,
    s = 0,
    l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }
  return {
    h,
    s,
    l,
    toString: function () {
      return `hsl(${this.h * 360}, ${this.s * 100}%, ${this.l * 100}%)`;
    },
  };
};
export const isValidJsonString = (str: string) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};

/**
 * This function converts an rgb string to hex string
 * @param rgbString - rgb string
 * @returns hex string
 */
export const rgbToHex = (rgbString: string) => {
  const rgb = rgbString.replace(/[^\d,]/g, '').split(',');
  const r = parseInt(rgb[0], 10);
  const g = parseInt(rgb[1], 10);
  const b = parseInt(rgb[2], 10);

  return `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`;
};

/**
 * This function converts a hex string to rgb string
 * @param hexString - hex string
 * @returns rgb string
 */
export const hexToRgb = (hexString: string) => {
  const hex = hexString.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgb(${r}, ${g}, ${b})`;
};

/**
 * This function converts an RGB string to a HSL string
 * @param rgbString - rgb string
 * @returns hsl Object
 */
export const rgbToHsl = (rgbString: string) => {
  rgbString = rgbString[0] === '#' ? hexToRgb(rgbString) : rgbString;
  const rgb = rgbString.replace(/[^\d,]/g, '').split(',');
  const r = parseInt(rgb[0], 10) / 255;
  const g = parseInt(rgb[1], 10) / 255;
  const b = parseInt(rgb[2], 10) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = (max + min) / 2;
  let s = (max + min) / 2;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;

    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
      default:
        break;
    }

    h /= 6;
  }

  return {
    h,
    s,
    l,
    toString: function () {
      return `hsl(${this.h * 360}, ${this.s * 100}%, ${this.l * 100}%)`;
    },
  };
};

/**
 * This functions checks whether a given string is a valid ISIN or not
 *
 * @param str - string to check
 */
export const isValidISIN = (str: string) => {
  const result = new RegExp(/^[A-Z]{2}[A-Z0-9]{9}\d$/).test(str);
  return result;
};
