import React from 'react';

import { useTranslation } from 'react-i18next';
import { SafeAreaView, StyleSheet, View } from 'react-native';

import { ThemeSelector } from '@/components/ThemeSelector';
import { ThemedText } from '@/components/global/ThemedText';
import { ThemedView } from '@/components/global/ThemedView';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export default function ThemeTestScreen() {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const gradientColors = colors.linearGradient;

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <View style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText type="bold" size={24}>
            {t('settings.themeTest.title')}
          </ThemedText>
          <ThemedText type="regular" size={16}>
            {t('settings.themeTest.description')}
          </ThemedText>
        </ThemedView>

        <View style={styles.content}>
          <ThemeSelector />
        </View>

        <ThemedView style={styles.footer}>
          <ThemedText type="semi-bold" size={14}>
            {t('settings.themeTest.gradientColors', {
              start: gradientColors[0],
              end: gradientColors[1],
            })}
          </ThemedText>
        </ThemedView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  footer: {
    padding: 15,
    alignItems: 'center',
  },
});
