import React from 'react';

import { CheckBox } from '@rneui/themed';
import { Pressable, ScrollView, View } from 'react-native';

import Accordion from '@/components/base/Accordion';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { t as tBase } from '@/i18n';

import { IStepProps } from '../SubscriptionModal';

export default function ExplainerStep({ setCanContinue, canContinue }: IStepProps) {
  const t = (key: string, data?: any) => tBase('subscription.choosePlan.steps.explainer.' + key, data);
  const { colors, isLight, isDark } = useTheme();

  return (
    <View className="flex-1">
      <ScrollView className="flex p-4 mb-2">
        <ThemedText className="mb-4 text-center" id="title" h2>
          {t('title')}
        </ThemedText>
        <ThemedText className="mb-4 px-5" id="description">
          {t('description')}
        </ThemedText>
        <Accordion
          className="mb-4"
          title={t('accordion.pricingOptions.title')}
          initiallyOpen={true}
          unitedBackground
          content={
            <View className="py-3 px-5">
              <ThemedText>{t('accordion.pricingOptions.content')}</ThemedText>
            </View>
          }
        />
        <Accordion
          className="mb-4"
          title={t('accordion.factorMeaning.title')}
          initiallyOpen={true}
          unitedBackground
          content={
            <View className="py-3 px-5">
              <ThemedText className="font-semibold">
                {t('accordion.factorMeaning.content.higherFactor.title')}
              </ThemedText>
              <ThemedText className="mb-3">
                {' '}
                {`\u2022`} {t('accordion.factorMeaning.content.higherFactor.bullet')}
              </ThemedText>
              <ThemedText className="font-semibold">
                {t('accordion.factorMeaning.content.lowerFactor.title')}
              </ThemedText>
              <ThemedText className="mb-3">
                {' '}
                {`\u2022`} {t('accordion.factorMeaning.content.lowerFactor.bullet')}
              </ThemedText>
              <ThemedText className="font-semibold">{t('accordion.factorMeaning.content.allCases.title')}</ThemedText>
              <ThemedText className="mb-3">
                {' '}
                {`\u2022`} {t('accordion.factorMeaning.content.allCases.bullet')}
              </ThemedText>
            </View>
          }
        />

        <Pressable
          onPress={() => setCanContinue(prev => !prev)}
          className="py-3 px-5 bg-secondary-light dark:bg-secondary-dark flex flex-row items-center justify-between rounded-xl mb-6"
        >
          <ThemedText>{t('understand')}</ThemedText>
          <CheckBox
            onPress={() => setCanContinue(prev => !prev)}
            wrapperStyle={{ backgroundColor: 'transparent', margin: 0, padding: 0 }}
            iconType="material-community"
            checkedIcon="checkbox-marked"
            uncheckedIcon="checkbox-blank-outline"
            checkedColor={colors.primary}
            containerStyle={{
              backgroundColor: 'transparent',
              margin: 0,
              padding: 0,
              marginLeft: 0,
              marginRight: 0,
            }}
            checked={canContinue}
          />
        </Pressable>
      </ScrollView>
    </View>
  );
}
