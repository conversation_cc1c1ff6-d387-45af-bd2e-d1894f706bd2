// filepath: /home/<USER>/Desktop/companion/components/features/actor/TopThreeTable/TopThreeTable.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface TopThreeTableProps {
  data: { id: string; name: string; value: string }[];
}

const TopThreeTable: React.FC<TopThreeTableProps> = ({ data }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Top Three Items</Text>
      {data.map(item => (
        <View key={item.id} style={styles.row}>
          <Text style={styles.cell}>{item.name}</Text>
          <Text style={styles.cell}>{item.value}</Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  cell: {
    fontSize: 16,
  },
});

export default TopThreeTable;