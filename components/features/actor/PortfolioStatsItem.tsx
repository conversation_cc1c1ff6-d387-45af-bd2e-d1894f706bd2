import React from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { useUserProfile } from '@/common/profile';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

export type PortfolioStatsItemProps = {
  title: string;
  value?: {
    amount: number;
    unit: string;
  };
  extraInfo?: React.ReactNode;
};

export default function PortfolioStatsItem({ title, value, extraInfo }: PortfolioStatsItemProps) {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();

  const unit = useUserProfile().profile?.flags.currency;

  return (
    <View className="mb-2 gap-3 flex flex-1 justify-between">
      <ThemedText textType="muted">{title}</ThemedText>
      <View className="flex flex-row justify-between items-end">
        <ThemedText size={24} type="bold">
          {t('currency', {
            amount: {
              amount: value?.amount ?? 0,
              unit: value?.unit ?? unit,
              options: {
                notation: 'compact',
              },
            },
          })}
        </ThemedText>
        {!!extraInfo && (
          <ThemedText size={12} textType="muted" style={{ marginBottom: 3 }}>
            {extraInfo}
          </ThemedText>
        )}
      </View>
    </View>
  );
}
