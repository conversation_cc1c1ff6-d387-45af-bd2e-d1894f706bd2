import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { CompanyDividendYield, DividendDisplayOption } from '@/types/actor-api.types';
import { SecurityAccountSecurity } from '@/types/secapi.types';

import usePortfolioQuery from '../../../hooks/actor/useDepotQuery';
import { ActorService } from '../../../services/actor.service';
import Widget from './Widget';

export type CompanyDividendYieldWidgetProps = {
  security: SecurityAccountSecurity;
};

export default function CompanyDividendYieldWidget({ security }: CompanyDividendYieldWidgetProps) {
  const { t } = useTranslation();
  const { colors, isDark } = useTheme();

  // Fetch company performance data for next payment info
  const { data: performanceData, isLoading: performanceLoading } = usePortfolioQuery({
    queryKey: ['companyPerformance', security.isin],
    queryFn: () => ActorService.getCompanyPerformance(security),
    enabled: !!security.isin,
  });

  // Fetch dividend history data
  const { data: dividendData, isLoading: dividendLoading } = usePortfolioQuery({
    queryKey: ['companyDividendsHistory', security.isin],
    queryFn: () => ActorService.getCompanyDividendsHistory(security, DividendDisplayOption.YIELDS),
    enabled: !!security.isin,
  });

  // Get the most recent dividend yield
  const currentDividendYield = dividendData?.dividendYields
    ?.slice()
    .sort(
      (a: CompanyDividendYield, b: CompanyDividendYield) => new Date(b.date).getTime() - new Date(a.date).getTime(),
    )[0];

  const isLoading = performanceLoading || dividendLoading;

  return (
    <Widget title={t('actor.dividendYieldWidget.title')} ready={!isLoading}>
      <View style={{ paddingVertical: 12 }}>
        {/* Dividend Yield Display */}
        <ExpoLinearGradient
          colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{
            paddingVertical: 20,
            paddingHorizontal: 16,
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {currentDividendYield?.dividendYield !== undefined && currentDividendYield?.dividendYield !== null ? (
            <View style={{ flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'center' }}>
              <ThemedText size={40} type="bold" style={{ color: colors.primary, lineHeight: 48 }}>
                {Number(currentDividendYield.dividendYield).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </ThemedText>
              <ThemedText size={32} type="bold" style={{ color: colors.text, marginLeft: 2, marginBottom: 4 }}>
                %
              </ThemedText>
            </View>
          ) : (
            <ThemedText size={40} type="bold">
              {t('common.notAvailable')}
            </ThemedText>
          )}
        </ExpoLinearGradient>

        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 16,
            marginTop: 6,
          }}
        >
          <ThemedText type="bold">{t('actor.dividendYieldWidget.nextPayment')}</ThemedText>
          <ThemedText>
            {performanceData?.nextPayment?.date
              ? t('dateTime.day', {
                  date: performanceData.nextPayment.date,
                })
              : t('common.notAvailable')}
          </ThemedText>
        </View>
      </View>
    </Widget>
  );
}
