import React from 'react';

import { Icon } from '@rneui/themed';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Pressable, SafeAreaView, ScrollView, StyleProp, View, ViewStyle } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { ModalManager } from '@/components/global/modal';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

type WidgetProps = {
  title?: string;
  children?: React.ReactNode;
  ready?: boolean;
  settings?: React.ReactNode;
  styles?: {
    container?: StyleProp<ViewStyle>;
    root?: StyleProp<ViewStyle>;
  };
  // Expandable widget props
  isExpandable?: boolean;
  subtitle?: string;
  expandedContent?: React.ReactNode;
  extraMargin?: boolean;
  titleAlign?: 'center' | 'flex-start';
};

interface ExpandableWidgetModalProps {
  title?: string;
  children?: React.ReactNode;
  subtitle?: string;
  expandedContent?: React.ReactNode;
  dismiss: () => void;
}

const ExpandableWidgetModal: React.FC<ExpandableWidgetModalProps> = ({
  title,
  children,
  subtitle,
  expandedContent,
  dismiss,
}) => {
  const { colors, isLight, isDark } = useTheme();

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
      {/* Header without border in modal */}
      <View
        className="px-4 pt-4 pb-4"
        style={{
          backgroundColor: colors.secondary,
        }}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-1">
            {title && (
              <ThemedText size={28} type="outfit-semi-bold" className="mb-1">
                {title}
              </ThemedText>
            )}
            {subtitle && <ThemedText textType="muted">{subtitle}</ThemedText>}
          </View>
          <Pressable onPress={dismiss} className="p-2 rounded-full" style={{ backgroundColor: colors.background }}>
            <Icon name="close" type="material" size={24} color={colors.text} />
          </Pressable>
        </View>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 p-4">{expandedContent || children}</ScrollView>
    </SafeAreaView>
  );
};

export default function Widget({
  title,
  children,
  ready = true,
  settings,
  styles,
  isExpandable = false,
  subtitle,
  expandedContent,
  extraMargin,
  titleAlign = 'center',
}: WidgetProps) {
  // Map 'flex-start' to 'left' for textAlign
  const textAlignValue = titleAlign === 'flex-start' ? 'left' : 'center';
  const { colors, isLight, isDark } = useTheme();

  const { t } = useTranslation();

  const handleExpand = () => {
    ModalManager.showModal(
      ExpandableWidgetModal,
      {
        title,
        children,
        subtitle,
        expandedContent,
      },
      {
        presentationStyle: 'fullScreen',
        animationType: 'slide',
      },
    );
  };

  // Loading state for expandable widget
  if (isExpandable && !ready) {
    return (
      <View style={{ marginBottom: 44 }}>
        <View
          className="rounded-xl mb-6 overflow-hidden"
          style={{
            backgroundColor: colors.background,
            borderTopWidth: 10,
            borderTopColor: colors.primary,
          }}
        >
          {/* Header */}
          {(title || subtitle) && (
            <View className="flex flex-row items-center w-full p-4" style={{ height: 70, justifyContent: titleAlign }}>
              {title && (
                <ThemedText size={28} type="outfit-semi-bold" style={{ textAlign: textAlignValue }}>
                  {title}
                </ThemedText>
              )}
            </View>
          )}
          {subtitle && (
            <ExpoLinearGradient
              colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <ThemedText textType="muted" size={14} className="text-center mt-2 mb-2">
                {subtitle}
              </ThemedText>
            </ExpoLinearGradient>
          )}

          {/* Loading content */}
          <View style={{ padding: 20, alignItems: 'center', minHeight: 200, justifyContent: 'center' }}>
            <ActivityIndicator color={colors.primary} size="large" />
          </View>
        </View>
      </View>
    );
  }

  // Expandable widget layout
  if (isExpandable) {
    return (
      <View style={{ marginBottom: 44 }}>
        <View
          className="rounded-xl mb-6 overflow-hidden"
          style={{
            backgroundColor: colors.background,
            borderTopWidth: 10,
            borderTopColor: colors.primary,
          }}
        >
          {/* Header */}
          {(title || subtitle) && (
            <View className="flex flex-row items-center w-full p-4" style={{ height: 70, justifyContent: titleAlign }}>
              {title && (
                <ThemedText size={28} type="outfit-semi-bold" style={{ textAlign: textAlignValue }}>
                  {title}
                </ThemedText>
              )}
            </View>
          )}
          {subtitle && (
            <ExpoLinearGradient
              colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <ThemedText textType="muted" size={14} className="text-center my-1">
                {subtitle}
              </ThemedText>
            </ExpoLinearGradient>
          )}

          <View className="overflow-hidden pb-5">
            {children || (
              <View style={{ padding: 20, alignItems: 'center', minHeight: 100, justifyContent: 'center' }}>
                <ThemedText size={16} style={{ textAlign: 'center', opacity: 0.5 }}>
                  {t('common:noContentAvailable')}
                </ThemedText>
              </View>
            )}
          </View>
        </View>
        <Pressable
          onPress={handleExpand}
          className="flex-row justify-center items-center"
          style={{
            position: 'absolute',
            left: '50%',
            marginLeft: -20, // Half of width (60) to center
            bottom: 0, // Position it fully inside the container
            zIndex: 999,
            backgroundColor: colors.primary,
            borderRadius: 25,
            height: 40,
            width: 40,
          }}
        >
          <Icon name="fullscreen" type="material" size={scaleFont(20)} color="white" />
        </Pressable>
      </View>
    );
  }

  // Regular widget layout
  return (
    <View
      style={[
        styles?.root,
        {
          backgroundColor: colors.background,
          borderRadius: 30,
          marginBottom: extraMargin ? 44 : 24,
        },
      ]}
    >
      {(!!title || !!settings) && (
        <View className="flex flex-row items-center w-full p-4" style={{ height: 70, justifyContent: titleAlign }}>
          <ThemedText size={28} type="outfit-semi-bold" style={{ textAlign: textAlignValue }}>
            {title}
          </ThemedText>
          {settings}
        </View>
      )}
      {subtitle && (
        <ExpoLinearGradient
          colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <ThemedText textType="muted" size={14} className="text-center my-1">
            {subtitle}
          </ThemedText>
        </ExpoLinearGradient>
      )}
      <View style={[styles?.container, { borderRadius: 0, flex: 1 }]}>
        {!ready ? (
          <View style={{ padding: 20, alignItems: 'center', minHeight: 100, justifyContent: 'center' }}>
            <ActivityIndicator color={colors.primary} size="large" />
          </View>
        ) : (
          children || (
            <View style={{ padding: 20, alignItems: 'center', minHeight: 100, justifyContent: 'center' }}>
              <ThemedText size={14} style={{ textAlign: 'center', opacity: 0.5 }}>
                {t('common:noContentAvailable')}
              </ThemedText>
            </View>
          )
        )}
      </View>
    </View>
  );
}
