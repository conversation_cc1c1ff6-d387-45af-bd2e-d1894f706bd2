import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import SectionList from '@/components/SectionList';
import { SafeAreaView, ScrollScreen } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export type Lesson = {
  title: string;
  firstView: [
    {
      title: string;
      content: string;
    },
  ];
  sections: [
    {
      title: string;
      questions: [string];
    },
  ];
};

export default function Learning() {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const lessons = t('learning.lessons.items', {
    returnObjects: true,
  }) as Array<Lesson>;

  return (
    <SafeAreaView
      style={{
        backgroundColor: colors.background,
      }}
    >
      <ScrollScreen>
        <ThemedText className="text-3xl font-bold mb-5 mx-1.5">{t('learning.title')}</ThemedText>
        <View className="mb-9 mx-1">
          <ThemedText className="text-[16px] mb-2.5">{t('learning.description')}</ThemedText>
        </View>
        <SectionList
          title={t('learning.lessons.title')}
          items={lessons.map((lesson, index) => ({
            title: lesson.title,
            onPress: () => router.push({ pathname: '/main/app/learning/lesson-details', params: { lessonId: index } }),
          }))}
        />
      </ScrollScreen>
    </SafeAreaView>
  );
}
