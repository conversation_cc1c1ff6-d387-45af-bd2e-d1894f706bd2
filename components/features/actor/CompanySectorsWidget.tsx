import React, { useMemo, useState } from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import { ActorService } from '@/services/actor.service';
import { SecurityAccountSecurity } from '@/types/actor-api.types';

import PieChart from './PieChart/PieChart';
import { PieDataItem } from './PieChart/PieChart';
// import PieChartLegend from './PieChart/PieChartLegend';
import Widget from './Widget';

interface SectorWidgetProps {
  security: SecurityAccountSecurity;
}

interface PieChartEntry extends PieDataItem {
  name: string;
  percentage: number;
}

export default function CompanySectorsWidget({ security }: SectorWidgetProps) {
  const { t } = useTranslation();
  const { colors } = useTheme();
  const [selectedSegment, setSelectedSegment] = useState<PieChartEntry | null>(null);

  // Fetch company sector data
  const { data: sectorData, isLoading: loading } = usePortfolioQuery({
    queryKey: ['companySectors', security.isin],
    queryFn: () => ActorService.getCompanySectors(security.isin),
    enabled: !!security.isin,
  });

  // Fetch company performance data
  const { data: companyPerformanceData, isLoading: loadingCompanyPerfomance } = usePortfolioQuery({
    queryKey: ['companyPerformance', security.isin],
    queryFn: () => ActorService.getCompanyPerformance(security),
    enabled: !!security.isin,
  });
  const entries: PieChartEntry[] = useMemo(() => {
    const entriesMap = new Map<string, { share: number; sector: string }>();

    // Aggregate shares by sector
    sectorData?.forEach(entry => {
      const sectorKey = entry.sector || 'other';
      const existingEntry = entriesMap.get(sectorKey);
      const amount = entry.share;
      if (existingEntry) {
        existingEntry.share += amount;
      } else {
        entriesMap.set(sectorKey, {
          share: amount,
          sector: entry.sector || 'other',
        });
      }
    });

    // Calculate total for percentage calculation
    const totalShares = Array.from(entriesMap.values()).reduce((sum, entry) => sum + entry.share, 0);

    // Generate a color palette based on the theme primary color
    const baseColor = colors.primary;
    // Helper to lighten/darken hex color
    function shadeColor(hex: string, percent: number) {
      let R = parseInt(hex.substring(1, 3), 16);
      let G = parseInt(hex.substring(3, 5), 16);
      let B = parseInt(hex.substring(5, 7), 16);
      R = Math.min(255, Math.max(0, R + (255 - R) * percent));
      G = Math.min(255, Math.max(0, G + (255 - G) * percent));
      B = Math.min(255, Math.max(0, B + (255 - B) * percent));
      return `#${R.toString(16).padStart(2, '0')}${G.toString(16).padStart(2, '0')}${B.toString(16).padStart(2, '0')}`;
    }

    const entriesArray = Array.from(entriesMap.entries()).map(([sectorKey, data], idx, arr) => {
      // Spread the color palette by lightening the primary color
      const color = shadeColor(baseColor, idx / Math.max(1, arr.length));
      return {
        id: sectorKey,
        name: t(`gicsSectors.${sectorKey}`),
        value: data.share,
        percentage: totalShares > 0 ? (data.share / totalShares) * 100 : 0,
        color,
      };
    });
    return entriesArray.sort((a, b) => b.percentage - a.percentage);
  }, [sectorData, companyPerformanceData, t, colors.primary]);

  if (!loading && !loadingCompanyPerfomance && entries.length === 0) {
    return (
      <Widget title={t('actor.sectorWidget.title')} ready={!loading}>
        <View className="flex-1 justify-center items-center py-8">
          <ThemedText textType="muted" className="text-center">
            {t('actor.sectorWidget.noData', { defaultValue: 'No sector data available' })}
          </ThemedText>
        </View>
      </Widget>
    );
  }

  const updateFocus = (selected: PieChartEntry | null) => {
    entries.forEach(segment => {
      segment.focused = selected ? segment === selected : false;
    });
  };

  const handlePress = (segment: PieChartEntry) => {
    const newSelected = segment?.focused ? null : segment;
    setSelectedSegment(newSelected);
    updateFocus(newSelected);
  };

  // Determine the color for the inner circle (match outer color of first entry or fallback)
  const innerCircleColor = selectedSegment?.color || entries[0]?.color || '#fff';

  // Custom legend rendering for this widget only
  const legendEntries = selectedSegment ? [selectedSegment] : entries.slice(0, 6);

  return (
    <Widget
      title={t('actor.sectorWidget.title')}
      ready={!loading && !loadingCompanyPerfomance}
      styles={{
        container: {
          alignItems: 'center',
          gap: 10,
        },
      }}
    >
      <PieChart
        data={entries}
        centerLabelComponent={() => <></>}
        selectedSegment={selectedSegment}
        setSelectedSegment={setSelectedSegment}
        variant="donut"
        // @ts-ignore
        innerCircleColor={innerCircleColor}
      />
      <View style={{ width: '100%', marginTop: 10, paddingVertical: 10 }}>
        {legendEntries.map(entry => (
          <View
            key={entry.id}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
              marginLeft: 8,
              paddingHorizontal: 16,
            }}
          >
            <View
              style={{
                height: 20,
                width: 20,
                borderRadius: 10,
                backgroundColor: entry.color,
                marginRight: 14,
              }}
            />
            <ThemedText size={12} type="bold">
              {`${Math.round(entry.percentage)}%`}
              {'  '}
            </ThemedText>
            <ThemedText size={12}>{entry.name}</ThemedText>
          </View>
        ))}
      </View>
    </Widget>
  );
}
