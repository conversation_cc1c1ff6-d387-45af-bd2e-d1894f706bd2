import { ActivityIndicator, View } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

export default function FullScreenActivityIndicator() {
  const { colors } = useTheme();
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
      <ActivityIndicator size="small" color={colors.text} />
    </View>
  );
}
