import React from 'react';

import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { Image, Pressable, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

export type HeaderProps = {
  title?: string;
  showBackButton?: boolean;
  showLogo?: boolean;
  onBackPress?: () => void;
  backgroundColor?: string;
  titleColor?: string;
};

export function Header({ title = '', showBackButton = true, onBackPress, titleColor }: HeaderProps) {
  const router = useRouter();
  const { colors, isLight, isDark } = useTheme();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 24,
    },
    topSection: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      marginBottom: 6,
    },
    backButton: {
      position: 'absolute',
      left: 0,
      borderRadius: 20,
      zIndex: 1,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
      width: '100%',
      alignSelf: 'center',
      backgroundColor: colors.background,
      borderRadius: 20,
      paddingVertical: 8,
      borderWidth: 1,
      borderColor: colors.border || colors.text + '20',
    },
    logo: {
      width: 24,
      height: 24,
      marginRight: 8,
    },
    headerImage: {
      width: 70,
      height: 53,
      resizeMode: 'cover',
    },
  });

  return (
    <View>
      <View style={styles.container}>
        <View style={styles.topSection}>
          {showBackButton && (
            <Pressable style={styles.backButton} onPress={handleBackPress}>
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </Pressable>
          )}
          <Image source={require('@/assets/images/header/header.png')} style={styles.headerImage} />
        </View>

        {title && (
          <View style={styles.titleContainer}>
            <ThemedText size={16} style={{ color: titleColor || colors.text, textAlign: 'center' }}>
              {title}
            </ThemedText>
          </View>
        )}
      </View>
    </View>
  );
}
