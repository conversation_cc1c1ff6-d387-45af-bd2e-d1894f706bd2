import React from 'react';

import { StyleSheet, TouchableOpacity } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

import { ThemedText } from './ThemedText';

interface PeriodButtonProps {
  period: string | number;
  isActive: boolean;
  onPress: () => void;
  label: string;
}

const PeriodButton: React.FC<PeriodButtonProps> = ({ isActive, onPress, label }) => {
  const { colors } = useTheme();
  return (
    <TouchableOpacity
      style={[styles.periodButton, isActive && [{ borderWidth: 2, borderColor: colors.primary }]]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText size={15} type="semi-bold" style={styles.text}>
        {label}
      </ThemedText>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  periodButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 22,
    paddingVertical: 3,
    marginTop: 10,
    marginBottom: 10,
    marginHorizontal: 2,
    minWidth: 0,
  },

  text: {
    textAlign: 'center',
  },
});

export default PeriodButton;
