import React from 'react';

import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { ThemedButton } from '../../global/ThemedButton';
import { ThemedCard } from '../../global/ThemedCard';

interface InsightsWidgetProps {
  onPress?: () => void;
}

export const InsightsWidget: React.FC<InsightsWidgetProps> = ({ onPress }) => {
  const { t } = useTranslation();

  return (
    <>
      <ThemedCard
        title={t('actor.insights.title')}
        description={t('actor.insights.description')}
        imageSource={require('@/assets/images/insights/insights.png')}
        onPress={onPress}
      />
      <View
        style={{
          marginBottom: 24,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <ThemedButton title={t('common.settings')} onPress={() => router.push('/main/app/(tabs)/profile')} />
        <ThemedButton title={t('common.help')} />
      </View>
    </>
  );
};

export default InsightsWidget;
