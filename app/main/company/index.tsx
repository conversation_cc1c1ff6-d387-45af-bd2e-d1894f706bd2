import React, { useCallback, useState } from 'react';

import { Icon } from '@rneui/themed';
import { useQuery } from '@tanstack/react-query';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import {
  ActivityIndicator,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';

import { apiGet } from '@/common/api';
import { TextInput } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

const searchCompanies = async (query: string): Promise<any[]> => {
  const response = await apiGet('/securities', { query });
  return response || [];
};

export default function Company({}) {
  const { t } = useTranslation();
  const { colors, isDark } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  const { data: searchResults = [], isLoading: isSearching } = useQuery({
    queryKey: ['companies', 'search', searchQuery],
    queryFn: () => searchCompanies(searchQuery),
    enabled: searchQuery.trim().length > 2,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  const debouncedSearch = useCallback(
    debounce((query: string) => {
      setSearchQuery(query);
    }, 500),
    [],
  );

  const handleSearchChange = (text: string) => {
    debouncedSearch(text);
  };

  // Map security type to display string (customize as needed)
  const getSecurityTypeLabel = (type: string) => {
    if (!type) return '';
    switch (type.toLowerCase()) {
      case 'stock':
        return 'Stock';
      case 'etf':
      case 'etfs':
        return 'ETFs';
      default:
        return type;
    }
  };

  const CompanyListItem = ({ item, index }: { item: any; index: number }) => {
    console.log(item);
    const [iconError, setIconError] = React.useState(false);
    const content = (
      <TouchableOpacity
        className="flex-row items-center p-4"
        onPress={() => {
          router.push(`/main/company/${item.isin}`);
        }}
        activeOpacity={0.8}
      >
        {!iconError ? (
          <Image
            source={{ uri: `https://actor-static.divizend.com/actor/logo/${item.isin}.png` }}
            style={{ width: 40, height: 40, borderRadius: 20, marginRight: 12 }}
            resizeMode="contain"
            onError={() => setIconError(true)}
          />
        ) : (
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              marginRight: 12,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <ThemedText type="bold">{item.name?.charAt(0)?.toUpperCase() || '?'}</ThemedText>
          </View>
        )}
        <View style={{ flex: 1 }}>
          <ThemedText type="bold">{item.name}</ThemedText>
          <View
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <ThemedText className="mt-1" textType="muted">
              {getSecurityTypeLabel(item.type)}
            </ThemedText>
            <ThemedText className="mt-1 text-xs text-gray-400" textType="muted">
              {item.isin}
            </ThemedText>
          </View>
        </View>
      </TouchableOpacity>
    );
    if (index % 2 === 0) {
      return (
        <ExpoLinearGradient
          colors={
            isDark
              ? ['rgba(255,255,255,0)', 'rgba(255,255,255,0.05)', 'rgba(255,255,255,0)']
              : ['rgba(0,0,0,0)', 'rgba(0,0,0,0.05)', 'rgba(0,0,0,0)']
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{ backgroundColor: 'transparent' }}
        >
          {content}
        </ExpoLinearGradient>
      );
    }
    return content;
  };

  const renderResultItem = ({ item, index }: { item: any; index: number }) => (
    <CompanyListItem item={item} index={index} />
  );

  return (
    <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : 'height'} className="flex-1 ">
      <View className="flex-row items-center px-4 py-2 gap-3">
        <TouchableOpacity onPress={() => router.back()}>
          <Icon size={scaleFont(24)} name="arrow-back" type="material" color={colors.text} />
        </TouchableOpacity>

        <TextInput
          style={{ flex: 1 }}
          placeholder={t('common.search.placeholder')}
          autoFocus
          onChangeText={handleSearchChange}
        />

        <View style={{ width: 24 }} />
      </View>

      {searchQuery.trim().length > 2 && (
        <View className="flex-1">
          <View className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
            <ThemedText className="text-sm text-gray-600 dark:text-gray-400">
              {t('common.search.showingResults')}
              <ThemedText type="bold">{`\"${searchQuery}\"`}</ThemedText>
            </ThemedText>
          </View>

          {isSearching && (
            <View className="flex-1 justify-center items-center">
              <ActivityIndicator size="large" color={colors.primary} />
              <ThemedText className="mt-2 text-gray-600 dark:text-gray-400">{t('common.search.searching')}</ThemedText>
            </View>
          )}

          {!isSearching && searchResults.length > 0 && (
            <FlatList
              data={searchResults}
              renderItem={renderResultItem}
              showsVerticalScrollIndicator={false}
              keyExtractor={item => item.isin.toString()}
              className="flex-1"
              keyboardShouldPersistTaps="handled"
              keyboardDismissMode="none"
            />
          )}

          {!isSearching && searchResults.length === 0 && searchQuery.trim().length > 2 && (
            <View className="flex-1 justify-center items-center">
              <ThemedText type="bold">{t('common.search.noResults')}</ThemedText>
            </View>
          )}
        </View>
      )}

      {searchQuery.trim().length <= 2 && (
        <View className="flex-1 justify-center items-center">
          <ThemedText type="bold">{t('common.search.startTyping')}</ThemedText>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}
