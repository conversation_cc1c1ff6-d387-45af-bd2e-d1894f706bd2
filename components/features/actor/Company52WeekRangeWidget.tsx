import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { Dimensions, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { ActorService } from '@/services/actor.service';
import { PerformanceCompanyResponse, Quote } from '@/types/actor-api.types';
import { SecurityAccountSecurity } from '@/types/secapi.types';

import usePortfolioQuery from '../../../hooks/actor/useDepotQuery';
import ReusableLineChart, { LineDataPoint } from './LineChart/ReusableLineChart';
import Widget from './Widget';

const { width: screenWidth } = Dimensions.get('window');

export type Company52WeekRangeWidgetProps = {
  security: SecurityAccountSecurity;
};

export default function Company52WeekRangeWidget({ security }: Company52WeekRangeWidgetProps) {
  const { t } = useTranslation();
  const { colors, isDark } = useTheme();

  // Query company performance
  const { data: performance, isLoading } = usePortfolioQuery<PerformanceCompanyResponse>({
    queryKey: ['companyPerformance', security.isin],
    queryFn: () => ActorService.getCompanyPerformance(security),
    enabled: !!security.isin,
  });

  // Extract low, high, and current quotes
  const low = performance?.yearChange?.low;
  const high = performance?.yearChange?.high;
  const current = performance?.quote;

  // Prepare chart data (order by value: low, current, high)
  const points: (LineDataPoint & { date?: number | string; key: string })[] = [];
  if (low) points.push({ value: low.price, dataPointText: t('actor.52week.low', 'Low'), date: low.time, key: 'low' });
  if (current)
    points.push({
      value: current.price,
      dataPointText: t('actor.52week.current', 'Current'),
      date: current.time,
      key: 'current',
    });
  if (high)
    points.push({ value: high.price, dataPointText: t('actor.52week.high', 'High'), date: high.time, key: 'high' });
  // Sort by value
  const chartData = points.sort((a, b) => a.value - b.value);

  function formatDate(date: number | string | undefined) {
    if (!date) return '';
    const d = typeof date === 'number' ? new Date(date * 1000) : new Date(date);
    return d.toLocaleDateString();
  }

  // Find min/max for axis
  const minValue = Math.min(...chartData.map(d => d.value));
  const maxValue = Math.max(...chartData.map(d => d.value));

  // Find index of current value for default selection
  const defaultSelectedIndex = chartData.findIndex(d => d.key === 'current');

  return (
    <Widget title={t('actor.52week.widgetTitle', '52 week low/high')} ready={!isLoading}>
      <View style={[styles.container, { paddingHorizontal: 0, marginHorizontal: 0, width: '100%', marginLeft: 4 }]}>
        {chartData.length === 3 ? (
          <ReusableLineChart
            series={[{ data: chartData }]}
            height={160}
            width={screenWidth}
            minValue={minValue}
            maxValue={maxValue}
            pointerLabelComponent={items => {
              const item = items[0];
              return (
                <ExpoLinearGradient
                  colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[styles.pointerLabel, { maxWidth: 180 }]}
                >
                  <ThemedText size={12} type="regular" style={{ marginBottom: 2 }}>
                    {formatDate(item?.date)}
                  </ThemedText>
                  <ThemedText size={12} type="bold">
                    {item?.value?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    {item &&
                      performance &&
                      (() => {
                        // Find which point this is (low, current, high)
                        if (item.value === low?.price) return ` ${low?.currency || ''}`;
                        if (item.value === high?.price) return ` ${high?.currency || ''}`;
                        if (item.value === current?.price) return ` ${current?.currency || ''}`;
                        return '';
                      })()}
                  </ThemedText>
                </ExpoLinearGradient>
              );
            }}
          />
        ) : (
          <View style={styles.noData}>
            <ThemedText>{t('common.notAvailable')}</ThemedText>
          </View>
        )}
        <View style={styles.labelsRow}>
          <View style={styles.labelCol}>
            <ThemedText size={12} type="regular">
              {t('actor.52week.low', 'Low')}
            </ThemedText>
            <ThemedText size={12} type="bold">
              {low
                ? `${low.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${low.currency || ''}`
                : '--'}
            </ThemedText>
          </View>
          <View style={styles.labelColEnd}>
            <ThemedText size={12} type="regular">
              {t('actor.52week.high', 'High')}
            </ThemedText>
            <ThemedText size={12} type="bold">
              {high
                ? `${high.price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ${high.currency || ''}`
                : '--'}
            </ThemedText>
          </View>
        </View>
      </View>
    </Widget>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  pointerLabel: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 80,
    maxWidth: 180,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  noData: {
    height: 160,
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
    paddingHorizontal: 24,
  },
  labelCol: {
    alignItems: 'flex-start',
    flex: 1,
  },
  labelColEnd: {
    alignItems: 'flex-end',
    flex: 1,
  },
});
