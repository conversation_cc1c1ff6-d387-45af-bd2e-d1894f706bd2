import React from 'react';

import { useTranslation } from 'react-i18next';

import GenericTable from '@/components/features/actor/GenericTable/GenericTable';
import Widget from '@/components/features/actor/Widget';

const metricsHeaders = [
  { key: 'metric', label: 'METRIC', flex: 1 },
  { key: 'value', label: 'VALUE', flex: 1 },
];
const metricsRows = [
  { metric: 'Market Capitalization', value: '€2.56 M' },
  { metric: 'Number of Shares', value: '€14.94 B' },
  { metric: '52-Week High/Low', value: '€221.80 - €144.29' },
  { metric: 'Dividend Yield', value: '0.55%' },
  { metric: 'Dividends TTM', value: '€0.94' },
  { metric: 'Beta', value: '1.21' },
  { metric: 'P/E Ratio (KGV)', value: '30.99' },
  { metric: 'PEG Ratio (KGWV)', value: '16.29' },
  { metric: 'P/B <PERSON>io (KBV)', value: '45.14' },
  { metric: 'P/S Ratio (KUV)', value: '7.50' },
];

const MetricsWidgetExample = () => {
  const { t } = useTranslation();
  return (
    <Widget title={t('Metrics')} ready={true}>
      <GenericTable headers={metricsHeaders} rows={metricsRows} />
    </Widget>
  );
};

export default MetricsWidgetExample;
