import React from 'react';

import { useTranslation } from 'react-i18next';
import { Image, Text, View } from 'react-native';

import GenericTable from '@/components/features/actor/GenericTable/GenericTable';
import Widget from '@/components/features/actor/Widget';
import { ThemedText } from '@/components/global/ThemedText';
import { actor } from '@/signals/actor';

const YieldTable: React.FC = () => {
  const { t } = useTranslation();
  const { loadingState, depot } = actor.value;
  let allDividends: Array<{
    id: string;
    exDate: string;
    payDate: string;
    amount: string;
    logo?: string;
  }> = [];
  if (depot && depot.securities) {
    Object.values(depot.securities as any).forEach((security: any) => {
      if (security.dividends && security.dividends.length > 0) {
        security.dividends.forEach((div: any, idx: number) => {
          allDividends.push({
            id: `${security.isin}-${idx}`,
            exDate: div.date ? new Date(div.date).toLocaleDateString() : '--',
            payDate: div.paymentDate ? new Date(div.paymentDate).toLocaleDateString() : '--',
            amount:
              div.yield && div.yield.amount !== undefined
                ? div.yield.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
                : '',
            logo: `https://actor-static.divizend.com/actor/logo/${security.isin}.png`,
          });
        });
      }
    });
  }
  allDividends.sort((a, b) => {
    const dateA = a.exDate ? new Date(a.exDate).getTime() : 0;
    const dateB = b.exDate ? new Date(b.exDate).getTime() : 0;
    return dateA - dateB;
  });
  const collapsedData = allDividends.slice(0, 4);
  const headers = [
    { key: 'exDate', label: t('actor.dividendTable.exDate'), flex: 1 },
    { key: 'payDate', label: t('actor.dividendTable.payDay'), flex: 1 },
    { key: 'amount', label: t('actor.dividendTable.amount'), flex: 1 },
  ];
  const toRows = (data: typeof allDividends) =>
    data.map(row => ({
      exDate: row.exDate,
      payDate: row.payDate,
      amount: (
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            gap: 5,
            alignItems: 'center',
          }}
        >
          <ThemedText>{row.amount}</ThemedText>
          {row.logo ? (
            <Image source={{ uri: row.logo }} style={{ width: 20, height: 20, borderRadius: 2, marginRight: 4 }} />
          ) : null}
        </View>
      ),
    }));
  return (
    <Widget
      isExpandable
      title={t('actor.upcomingDividends.title')}
      ready={true}
      expandedContent={<GenericTable headers={headers} rows={toRows(allDividends)} />}
    >
      {<GenericTable headers={headers} rows={toRows(collapsedData)} isLoading={!loadingState.READY} />}
    </Widget>
  );
};

export default YieldTable;
