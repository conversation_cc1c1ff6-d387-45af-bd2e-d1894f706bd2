import React from 'react';

import { Image, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

interface AssetTableRowProps {
  icon: string;
  name: string;
  subtitle: string;
  percentage?: string;
  value: string;
  percentageColor?: string;
  valueUnderline?: boolean;
  rightLabel?: string;
  rightLabelColor?: string;
  titleValue?: string;
  isTopAsset?: boolean;
}

const AssetTableRow: React.FC<AssetTableRowProps> = ({
  icon,
  name,
  subtitle,
  percentage,
  value,
  percentageColor,
  valueUnderline = false,
  rightLabel,
  rightLabelColor,
  titleValue,
  isTopAsset,
}) => {
  const { colors } = useTheme();
  const mainColor = colors.primary;
  return (
    <View style={[styles.row]}>
      <Image source={{ uri: icon }} style={styles.icon} />
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <ThemedText type="semi-bold" size={15} numberOfLines={1}>
            {name}
          </ThemedText>
          {isTopAsset && titleValue && (
            <ThemedText size={15} style={{ color: mainColor, marginLeft: 4 }} numberOfLines={1}>
              {titleValue}
            </ThemedText>
          )}
        </View>
        <ThemedText size={13} numberOfLines={1}>
          {subtitle}
        </ThemedText>
      </View>
      <View style={{ alignItems: 'flex-end' }}>
        {percentage && (
          <ThemedText type="bold" size={15} style={{ color: percentageColor || mainColor }} numberOfLines={1}>
            {percentage}
          </ThemedText>
        )}
        <ThemedText size={14} style={[{ color: colors.text }]} numberOfLines={1}>
          {value}
        </ThemedText>
        {rightLabel && (
          <ThemedText type="bold" size={13} style={{ color: rightLabelColor || mainColor }} numberOfLines={1}>
            {rightLabel}
          </ThemedText>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 4,
    padding: 12,
  },
  icon: {
    width: 28,
    height: 28,
    marginRight: 12,
    borderRadius: 6,
  },
  // name, subtitle, percentage, value, rightLabel styles are now handled by ThemedText
  underline: {
    textDecorationLine: 'underline',
  },
  rightLabel: {
    fontSize: 13,
    fontWeight: 'bold',
  },
});

export default AssetTableRow;
