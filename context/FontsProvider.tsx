import React, { createContext, useContext, useEffect, useState } from 'react';

import { useFonts } from 'expo-font';
import { SplashScreen } from 'expo-router';

const FontsLoadedContext = createContext(false);

export function useFontsLoaded() {
  return useContext(FontsLoadedContext);
}

export function FontsProvider({ children }: { children: React.ReactNode }) {
  const [loaded] = useFonts({
    OutfitRegular: require('../assets/fonts/Outfit-Regular.ttf'),
    OutfitSemiBold: require('../assets/fonts/Outfit-SemiBold.ttf'),
    OutfitBold: require('../assets/fonts/Outfit-Bold.ttf'),
    TintRegular: require('../assets/fonts/Inter_24pt-Regular.ttf'),
    TintSemiBold: require('../assets/fonts/Inter_24pt-SemiBold.ttf'),
    TintBold: require('../assets/fonts/Inter_24pt-Bold.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  return <FontsLoadedContext.Provider value={loaded}>{children}</FontsLoadedContext.Provider>;
}
