import React from 'react';

import { But<PERSON> } from '@rneui/themed';
import { StyleSheet, View, ViewProps } from 'react-native';
import { Text, TouchableOpacity } from 'react-native';

import { ColorScheme } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

import { ThemedText } from '../global/ThemedText';

interface OnboardingCardProps extends ViewProps {
  children: React.ReactNode;
  buttonLabel: string;
  onButtonPress: () => void;
  buttonDisabled?: boolean;
  buttonLoading?: boolean;
}

export const OnboardingCard: React.FC<OnboardingCardProps> = ({
  children,
  buttonLabel,
  onButtonPress,
  buttonDisabled,
  buttonLoading,
  style,
  ...rest
}) => {
  const { colors } = useTheme();
  const createStyles = (colors: ColorScheme) =>
    StyleSheet.create({
      card: {
        backgroundColor: colors.background,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        borderBottomRightRadius: 20,
        borderBottomLeftRadius: 0,
        borderWidth: 2,
        width: '90%',
        alignItems: 'center',
        paddingTop: 24,
        paddingBottom: 52, // increased to make space for button overflow
        overflow: 'visible',
      },
      content: {
        alignItems: 'center',
        width: '100%',
      },
      buttonWrapper: {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: -28, // half out, half in
        alignItems: 'center',
        zIndex: 10,
      },
      button: {
        borderRadius: 56,
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderWidth: 2,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.primary,
        borderColor: colors.primary,
      },
      buttonText: {
        letterSpacing: 1,
        color: 'white',
      },
    });
  const styles = createStyles(colors);
  return (
    <View style={[styles.card, { borderColor: colors.primary }, style]} {...rest}>
      <View style={styles.content}>{children}</View>
      {buttonLabel && onButtonPress && (
        <View style={styles.buttonWrapper} pointerEvents="box-none">
          <TouchableOpacity
            style={styles.button}
            onPress={onButtonPress}
            activeOpacity={0.7}
            disabled={buttonDisabled || buttonLoading}
          >
            <ThemedText style={styles.buttonText} type="semi-bold" size={16}>
              {buttonLabel}
            </ThemedText>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
