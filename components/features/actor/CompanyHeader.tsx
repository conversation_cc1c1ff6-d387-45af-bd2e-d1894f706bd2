import React, { useState } from 'react';

import { Image, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

import Widget from './Widget';

interface CompanyHeaderProps {
  name: string;
  isin: string;
  size?: 'small' | 'medium' | 'large';
}

export default function CompanyHeader({ name, isin, size = 'medium' }: CompanyHeaderProps) {
  const { colors, isLight, isDark } = useTheme();

  const [imageError, setImageError] = useState(false);

  const getLogoUrl = (isin: string) => {
    return `https://actor-static.divizend.com/actor/logo/${isin}.png`;
  };

  const getCountryFlagUrl = (isin: string) => {
    const countryCode = isin.substring(0, 2).toUpperCase();
    return `https://divizend.com/flags/${countryCode}.png`;
  };
  return (
    <Widget ready>
      <View
        style={{
          borderWidth: 1,
          borderColor: colors.border,
          borderRadius: 30,
        }}
      >
        <View
          style={{
            backgroundColor: colors.background,
            borderRadius: 30,
            paddingHorizontal: 16,
            height: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          {/* Company Logo */}
          <View style={{ width: 28, alignItems: 'center' }}>
            {isin && !imageError ? (
              <Image
                source={{ uri: getLogoUrl(isin) }}
                style={{
                  width: 28,
                  height: 28,
                  borderRadius: 8,
                }}
                onError={() => setImageError(true)}
                resizeMode="contain"
              />
            ) : (
              <View
                style={{
                  width: 28,
                  height: 28,
                  backgroundColor: colors.primary,
                  borderRadius: 8,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <ThemedText
                  style={{
                    color: colors.background,
                  }}
                >
                  {name.charAt(0).toUpperCase()}
                </ThemedText>
              </View>
            )}
          </View>

          {/* Company Name - Centered */}
          <View style={{ flex: 1, alignItems: 'center', paddingHorizontal: 5 }}>
            <ThemedText
              style={{
                textAlign: 'center',
              }}
              type="semi-bold"
              size={24}
              numberOfLines={1}
            >
              {name}
            </ThemedText>
          </View>

          {/* Country Flag */}
          <View style={{ width: 28, alignItems: 'center' }}>
            {isin && (
              <Image
                source={{ uri: getCountryFlagUrl(isin) }}
                style={{
                  width: 28,
                  height: 28,
                  borderRadius: 4,
                }}
                resizeMode="cover"
              />
            )}
          </View>
        </View>
      </View>
    </Widget>
  );
}
