import React from 'react';

import { Icon } from '@rneui/base';
import { capitalize } from 'lodash';
import { useTranslation } from 'react-i18next';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';

import { clsx } from '@/common/clsx';
import { useUserProfileQuery } from '@/common/queries';
import { SelectModal } from '@/components/base/SelectModal';
import { BankParentIcon } from '@/components/features/portfolio-overview/BankParentIcon';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { actor } from '@/signals/actor';
import { scaleFont } from '@/utils/scaler';

interface PortfolioSelectorProps {
  style: StyleProp<ViewStyle>;
}

export const PortfolioSelector: React.FC<PortfolioSelectorProps> = ({ style }) => {
  const { colors, isLight, isDark } = useTheme();
  const { t } = useTranslation();

  const { data } = useUserProfileQuery();
  const depots = data?.depots ?? [];

  return (
    <SelectModal
      style={style}
      items={depots}
      labelExtractor={depot => depot.bankName ?? 'Depot'}
      placeholder={t('actor.portfolio.selector')}
      modalTitle={t('actor.portfolio.pickPortfolios')}
      searchPlaceholder={t('actor.portfolio.searchPlaceholder')}
      showDragger={false}
      selectedItems={
        depots?.filter(depot => actor.value.depotIds.includes(depot.id) || actor.value.depotIds === 'all') ?? []
      }
      onSelect={items => {
        if (items.length === 0) {
          actor.value = {
            ...actor.value,
            depotIds: 'all',
          };
          return;
        }
        actor.value = {
          ...actor.value,
          depotIds: items.map(item => item.id),
        };
      }}
      renderItem={(depot, isSelected, onPress) => (
        <TouchableOpacity onPress={onPress} className={clsx('flex-row items-center py-3 px-0 mb-2')}>
          {/* Left side - Checkbox and Bank Icon */}
          <View className="flex-row items-center ">
            <View
              style={{
                height: 20,
                width: 20,
                borderRadius: 4,
                backgroundColor: colors.desactivated,
                marginRight: 10,
              }}
            >
              {isSelected && (
                <View className="h-full w-full flex justify-center items-center">
                  <Icon name="check" type="antdesign" size={16} color={colors.text} />
                </View>
              )}
            </View>
            <View
              style={{
                marginRight: 10,
              }}
            >
              <BankParentIcon bankParent={depot.bankType} size={scaleFont(36)} />
            </View>
          </View>

          {/* Middle - Portfolio Info */}
          <View className="flex-1 ml-5 mr-2">
            <ThemedText
              size={18}
              type="semi-bold"
              className="font-medium text-base "
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {capitalize(depot.bankName)}
            </ThemedText>
            <ThemedText
              size={12}
              type="regular"
              className="text-sm"
              numberOfLines={1}
              ellipsizeMode="tail"
              textType="muted"
            >
              {depot.number || depot.description || depot.id}
            </ThemedText>
          </View>

          {/* Right side - Arrow */}
          <Icon name="chevron-right" type="evilicon" size={28} color={colors.text} />
        </TouchableOpacity>
      )}
    />
  );
};
