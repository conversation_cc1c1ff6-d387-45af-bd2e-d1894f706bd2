import React from 'react';

import { ViewProps } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ThemedView } from '../global/ThemedView';

type Props = ViewProps & {
  noMarginBottom?: boolean;
};

export function SafeAreaView({ children, style, noMarginBottom = false, ...props }: Props) {
  const insets = useSafeAreaInsets();
  return (
    <ThemedView
      style={{ flex: 1, marginBottom: noMarginBottom ? 0 : 40, paddingTop: insets.top, paddingBottom: insets.bottom }}
    >
      {children}
    </ThemedView>
  );
}
