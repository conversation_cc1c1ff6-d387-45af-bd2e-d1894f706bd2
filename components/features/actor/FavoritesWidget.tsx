import React, { useRef, useState } from 'react';

import { useTranslation } from 'react-i18next';
import { Dimensions, Image, ScrollView, StyleSheet, Text, View } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

import Widget from './Widget';

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  scroll: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  item: {
    alignItems: 'center',
    marginRight: 18,
  },
  logo: {
    width: 25,
    height: 25,
    marginBottom: 4,
    borderRadius: 8,
  },
  logoExpanded: {
    width: 40,
    height: 40,
    marginBottom: 4,
    borderRadius: 8,
  },
  name: {
    fontSize: 12,
    marginTop: 2,
    textAlign: 'center',
    maxWidth: 60,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 2,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 2,
  },
});

const favorites = [
  { name: '<PERSON>', logo: 'https://logo.clearbit.com/apple.com' },
  { name: 'Google', logo: 'https://logo.clearbit.com/google.com' },
  { name: 'Microsoft', logo: 'https://logo.clearbit.com/microsoft.com' },
  { name: 'Amazon', logo: 'https://logo.clearbit.com/amazon.com' },
  { name: 'Netflix', logo: 'https://logo.clearbit.com/netflix.com' },
  { name: 'Apple', logo: 'https://logo.clearbit.com/apple.com' },
  { name: 'Google', logo: 'https://logo.clearbit.com/google.com' },
  { name: 'Microsoft', logo: 'https://logo.clearbit.com/microsoft.com' },
  { name: 'Amazon', logo: 'https://logo.clearbit.com/amazon.com' },
  { name: 'Netflix', logo: 'https://logo.clearbit.com/netflix.com' },
  { name: 'Tesla', logo: 'https://logo.clearbit.com/tesla.com' },
  { name: 'Apple', logo: 'https://logo.clearbit.com/apple.com' },
  { name: 'Google', logo: 'https://logo.clearbit.com/google.com' },
  { name: 'Microsoft', logo: 'https://logo.clearbit.com/microsoft.com' },
  { name: 'Amazon', logo: 'https://logo.clearbit.com/amazon.com' },
  { name: 'Netflix', logo: 'https://logo.clearbit.com/netflix.com' },
  { name: 'Tesla', logo: 'https://logo.clearbit.com/tesla.com' },
  { name: 'Apple', logo: 'https://logo.clearbit.com/apple.com' },
  { name: 'Google', logo: 'https://logo.clearbit.com/google.com' },
  { name: 'Microsoft', logo: 'https://logo.clearbit.com/microsoft.com' },
  { name: 'Amazon', logo: 'https://logo.clearbit.com/amazon.com' },
  { name: 'Netflix', logo: 'https://logo.clearbit.com/netflix.com' },
  { name: 'Tesla', logo: 'https://logo.clearbit.com/tesla.com' },
  { name: 'Tesla', logo: 'https://logo.clearbit.com/tesla.com' },
];

export default function FavoritesWidget() {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const scrollRef = useRef(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [pageWidth, setPageWidth] = useState(0);
  const itemsPerScreen = 6; // Number of logos visible at once (adjust as needed)
  const totalPages = pageWidth > 0 ? Math.ceil(favorites.length / itemsPerScreen) : 0;

  // Dynamic dot styles
  const activeDot = (color: string) => ({ backgroundColor: color });
  const inactiveDot = (color: string) => ({ backgroundColor: color, opacity: 0.3 });

  // Handle scroll event to update current index
  const handleScroll = (event: any) => {
    const x = event.nativeEvent.contentOffset.x;
    const width = event.nativeEvent.layoutMeasurement.width;
    const page = Math.round(x / width);
    setCurrentIndex(page);
  };

  // Handle layout to get the width of the ScrollView
  const handleLayout = (event: any) => {
    const { width } = event.nativeEvent.layout;
    setPageWidth(width);
  };

  // Collapsed content: logos in a row, dots below in a column layout
  const collapsedContent = (
    <View style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
      <ScrollView
        ref={scrollRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        style={styles.scroll}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        onLayout={handleLayout}
      >
        {Array.from({ length: totalPages }).map((_, pageIdx) => (
          <View
            key={`page-${pageIdx}`}
            style={{
              flexDirection: 'row',
              width: pageWidth,
              justifyContent: 'space-between',
            }}
          >
            {favorites.slice(pageIdx * itemsPerScreen, (pageIdx + 1) * itemsPerScreen).map((fav, favIdx) => (
              <View key={`fav-${fav.name}-${pageIdx}-${favIdx}`} style={styles.item}>
                <Image
                  source={{ uri: fav.logo }}
                  style={[styles.logo, { backgroundColor: colors.inputBackground }]}
                  resizeMode="contain"
                />
              </View>
            ))}
          </View>
        ))}
      </ScrollView>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          marginTop: 16,
          paddingHorizontal: 16,
          paddingTop: 12,
          gap: 0,
          alignSelf: 'flex-start',
        }}
      >
        {Array.from({ length: Math.max(1, totalPages) }).map((_, idx) => (
          <View
            key={`dot-${idx}`}
            style={[
              styles.dot,
              { marginHorizontal: 3, marginVertical: 0 },
              idx === currentIndex ? activeDot(colors.primary) : inactiveDot(colors.border),
            ]}
          />
        ))}
      </View>
    </View>
  );

  // Expanded content: logos and names in a vertical scroll, bigger logo, bold name
  const expandedContent = (
    <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
      {favorites.map((fav, idx) => (
        <View
          key={`expanded-fav-${fav.name}-${idx}`}
          style={{ flexDirection: 'column', alignItems: 'flex-start', marginBottom: 18 }}
        >
          <Image
            source={{ uri: fav.logo }}
            style={[styles.logoExpanded, { backgroundColor: colors.inputBackground }]}
            resizeMode="contain"
          />
          <Text style={[styles.name, { color: colors.text, fontWeight: 'bold', marginTop: 6 }]}>{fav.name}</Text>
        </View>
      ))}
    </ScrollView>
  );

  return (
    <Widget
      title={t('actor.favorites.title')}
      titleAlign="flex-start"
      isExpandable
      expandedContent={expandedContent}
      styles={{ container: styles.container }}
    >
      {collapsedContent}
    </Widget>
  );
}
