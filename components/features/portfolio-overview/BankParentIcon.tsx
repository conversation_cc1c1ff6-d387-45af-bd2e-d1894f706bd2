import React, { useMemo, useState } from 'react';

import { Icon } from '@rneui/themed';
import { View } from 'react-native';
import { SvgUri } from 'react-native-svg';

import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

interface BankParentIconProps {
  bankParent?: string;
  size?: number;
}

export function BankParentIcon({ bankParent, size = 44 }: BankParentIconProps) {
  const [icon, setIcon] = useState<string>(bankParent?.endsWith('_UNKNOWN') ? 'UNKNOWN' : bankParent || 'UNKNOWN');
  const { colors, isLight, isDark } = useTheme();

  const iconUri = useMemo(() => {
    return 'https://divizend.com' + iconPath(icon);
  }, [icon]);

  return (
    <View
      style={{
        width: size,
        height: size,
        backgroundColor: colors.primary,
        borderRadius: 8,
        overflow: 'hidden',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {icon === 'UNKNOWN' ? (
        <Icon name="account-balance" type="material" size={scaleFont(size - 12)} color={colors.background} />
      ) : (
        <SvgUri
          width={size}
          height={size}
          uri={iconUri}
          onError={() => {
            setIcon('UNKNOWN');
          }}
        />
      )}
    </View>
  );
}

const iconPath = (icon: string) => {
  return `/bank/${icon}.svg`;
};
