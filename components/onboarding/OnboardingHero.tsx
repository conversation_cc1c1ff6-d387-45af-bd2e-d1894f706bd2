import React from 'react';

import { Icon } from '@rneui/base';
import { Dimensions, Image, StyleSheet, TouchableOpacity, View } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');

interface OnboardingHeroProps {
  isLight: boolean;
  showBackButton?: boolean;
  onBack?: () => void;
  showImages?: boolean; // new prop to control images visibility
}

export const OnboardingHero: React.FC<OnboardingHeroProps> = ({
  isLight,
  showBackButton,
  onBack,
  showImages = true,
}) => {
  const { colors } = useTheme();
  const containerStyle = [styles.container, !showImages && { height: 0 }];
  return (
    <View style={containerStyle}>
      {showBackButton && (
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Icon name="arrow-back" type="material" color={colors.text} size={28} />
        </TouchableOpacity>
      )}
      {showImages && (
        <View style={styles.imagesContainer}>
          <Image
            source={
              isLight ? require('@/assets/images/DIVIZEND_LIGHT.png') : require('@/assets/images/DIVIZEND_DARK.png')
            }
            style={styles.logo}
            resizeMode="contain"
          />
          <View style={{ height: 32 }} />
          <Image source={require('@/assets/images/CHAT.png')} style={styles.bubble} resizeMode="contain" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: screenHeight * 0.5,
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    marginTop: 20,
  },
  imagesContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  logo: {
    width: 183,
    height: 24,
    alignSelf: 'center',
  },
  bubble: {
    width: 346,
    height: 264,
    alignSelf: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 16,
    zIndex: 10,
  },
});
