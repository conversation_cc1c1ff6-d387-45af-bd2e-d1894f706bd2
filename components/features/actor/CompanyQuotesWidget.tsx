import React, { useMemo, useRef, useState } from 'react';

import { LineGraph } from '@divizend/react-native-graph';
import { useSignals } from '@preact/signals-react/runtime';
import { Icon } from '@rneui/themed';
import { throttle } from 'lodash';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';
import { runOnJS, useAnimatedReaction, useSharedValue } from 'react-native-reanimated';

import { clsx } from '@/common/clsx';
import { ThemedText } from '@/components/global/ThemedText';
import { showCustom } from '@/components/global/prompt';
import { useTheme } from '@/context/ThemeContext';
import { actor } from '@/signals/actor';
import { ExtendedQuote, PerformanceQuotesType, QuoteRange } from '@/types/actor-api.types';
import { scaleFont } from '@/utils/scaler';

import { createPerformanceQuotesField, useActorSettingsModal } from './ActorSettingsModal';
import Widget from './Widget';

interface QuotesWidgetProps {
  queryKey: (range: QuoteRange) => string[];
  queryFn: (range: QuoteRange) => Promise<ExtendedQuote[]>;
  useQuery: (options: { queryFn: () => Promise<ExtendedQuote[]>; queryKey: string[] }) => {
    data: ExtendedQuote[] | undefined;
    isLoading: boolean;
  };
  enableTWROR?: boolean;
}

const Info = ({
  quote,
  currentQuote,
  range,
  mode = PerformanceQuotesType.PERFORMANCE,
}: {
  quote: ExtendedQuote | undefined;
  currentQuote: ExtendedQuote;
  range: QuoteRange;
  mode?: PerformanceQuotesType;
}) => {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();

  const color =
    currentQuote.price - (quote?.price ?? 0) > 0
      ? '#078566' // Use green theme color
      : currentQuote.price - (quote?.price ?? 0) < 0
        ? '#C84279' // Use red theme color
        : colors.tabIconDefault;

  const arrowIcon = (() => {
    if (!quote) return null;
    const priceDifference = currentQuote.price - quote.price;
    if (priceDifference > 0) {
      return <Icon name="arrow-upward" size={scaleFont(15)} color="#078566" type="material" />;
    } else if (priceDifference < 0) {
      return <Icon name="arrow-downward" size={scaleFont(15)} color="#C84279" type="material" />;
    }
    return null;
  })();

  const twrorArrowIcon = (() => {
    if (!quote) return null;
    const twrorDifference = quote.twror;
    if (twrorDifference > 0) {
      return <Icon name="arrow-upward" size={scaleFont(25)} color={colors.text} type="material" />;
    } else if (twrorDifference < 0) {
      return <Icon name="arrow-downward" size={scaleFont(25)} color="#C84279" type="material" />;
    }
    return null;
  })();

  const mwrorArrowIcon = (() => {
    if (!quote) return null;
    const mwrorDifference = quote.mwror;
    if (mwrorDifference > 0) {
      return <Icon name="arrow-upward" size={25} color={colors.text} type="material" />;
    } else if (mwrorDifference < 0) {
      return <Icon name="arrow-downward" size={25} color="red" type="material" />;
    }
    return null;
  })();

  return (
    <>
      <ThemedText className=" text-gray-600 font-bold text-lg">
        {t(!(range === QuoteRange.Y || range === QuoteRange.ALL) ? 'dateTime.dayLongAndTime' : 'dateTime.dayLongUTC', {
          date: new Date((quote?.time ?? 0) * 1000),
        }).replace(/(\d{2}):(\d{2}):\d{2}/g, '$1:$2')}
      </ThemedText>
      {mode === PerformanceQuotesType.TWROR ? (
        <ThemedText
          h1
          type="bold"
          className={clsx('flex items-center')}
          style={{ color: (quote?.twror ?? 0) < 0 ? 'red' : colors.text }}
        >
          {twrorArrowIcon}
          {t('percent', {
            value: {
              value: quote?.twror ?? 0,
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
              signDisplay: 'exceptZero',
            },
          })}
        </ThemedText>
      ) : mode === PerformanceQuotesType.MWROR ? (
        <ThemedText
          h1
          type="bold"
          className={clsx('flex items-center')}
          style={{ color: (quote?.mwror ?? 0) < 0 ? 'red' : colors.text }}
        >
          {mwrorArrowIcon}
          {t('percent', {
            value: {
              value: quote?.mwror ?? 0,
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
              signDisplay: 'exceptZero',
            },
          })}
        </ThemedText>
      ) : (
        <ThemedText h1 className="font-bold">
          {t('currency', {
            amount: {
              amount: quote?.price ?? 0,
              unit: quote?.currency ?? 'EUR',
            },
          })}
        </ThemedText>
      )}
      <View
        className={clsx(
          'flex-row items-center',
          (mode === PerformanceQuotesType.TWROR || mode === PerformanceQuotesType.MWROR) && 'hidden',
        )}
      >
        {arrowIcon}
        <ThemedText
          h4
          style={{
            color: color,
            marginRight: 12,
          }}
        >
          {currentQuote.price - (quote?.price ?? 0) === 0 && '±'}
          {t('percent', {
            value: {
              value: (currentQuote.price - (quote?.price ?? 0)) / currentQuote.price,
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
              signDisplay: 'exceptZero',
            },
          })}
        </ThemedText>

        {arrowIcon}
        <ThemedText
          h4
          style={{
            color: color,
          }}
        >
          {currentQuote.price - (quote?.price ?? 0) === 0 && '±'}
          {t('currency', {
            amount: {
              amount: currentQuote.price - (quote?.price ?? 0),
              unit: quote?.currency ?? 'EUR',
              options: {
                signDisplay: 'exceptZero',
              },
            },
          })}
        </ThemedText>
      </View>
    </>
  );
};

export default function CompanyQuotesWidget({ queryFn, useQuery, queryKey, enableTWROR = false }: QuotesWidgetProps) {
  const { t } = useTranslation();
  const { colors, isLight, isDark } = useTheme();

  const isPanning = useRef(false);

  const selectedQuoteShared = useSharedValue<ExtendedQuote | undefined>(undefined);

  const [selectedQuote, setSelectedQuote] = useState<ExtendedQuote>();
  const [range, setRange] = useState<QuoteRange>(QuoteRange.Y);

  const { data: quotes = [], isLoading } = useQuery({
    queryFn: () => queryFn(range),
    queryKey: queryKey(range),
  });
  useSignals();

  const currentQuote = quotes.at(-1);

  const mode = enableTWROR
    ? (actor.value.settings?.performanceQuotesWidget.type ?? PerformanceQuotesType.PERFORMANCE)
    : PerformanceQuotesType.PERFORMANCE;

  useAnimatedReaction(
    () => selectedQuoteShared.value,
    value => {
      if (value) runOnJS(setSelectedQuote)(value);
    },
  );
  const SettingsModalComponent = useActorSettingsModal([createPerformanceQuotesField()]);

  const points = useMemo(() => {
    return quotes.map(q => ({
      date: new Date(q.time * 1000),
      value: mode === PerformanceQuotesType.TWROR ? q.twror : mode === PerformanceQuotesType.MWROR ? q.mwror : q.price,
    }));
  }, [quotes, mode]);

  const purchaseAmountPoints = useMemo(() => {
    return quotes.map(q => ({
      date: new Date(q.time * 1000),
      value: q.purchaseValue,
    }));
  }, [quotes]);

  const rangePoints = useMemo(() => {
    const key =
      mode === PerformanceQuotesType.TWROR ? 'twror' : mode === PerformanceQuotesType.MWROR ? 'mwror' : 'price';
    const maxQuote = quotes.reduce((max, quote) => (quote[key] > max ? quote[key] : max), 0);
    const minQuote = quotes.reduce((min, quote) => (quote[key] < min ? quote[key] : min), maxQuote);
    if (!quotes.length) return undefined;
    return {
      x: {
        min: new Date(quotes[0].time * 1000),
        max: new Date(((quotes[quotes.length - 1].time - quotes[0].time) * 1.25 + quotes[0].time) * 1000),
      },
      y: {
        min: minQuote,
        max: maxQuote,
      },
    };
  }, [quotes, mode, range]);

  return (
    <Widget
      title={t('actor.quotes.title')}
      ready={!isLoading}
      styles={{ root: { overflow: 'hidden' } }}
      settings={
        enableTWROR ? (
          <Pressable onPress={() => showCustom(SettingsModalComponent)}>
            <Icon name="settings" type="material" color="gray" size={24} />
          </Pressable>
        ) : undefined
      }
    >
      {!isLoading && quotes.length > 0 && (
        <>
          <Info mode={mode} quote={selectedQuote ?? currentQuote} currentQuote={currentQuote!} range={range} />

          <ThemedText className="text-center text-gray-500 text-xs mb-2 italic">
            {t('actor.chartInstruction')}
          </ThemedText>

          <View>
            {mode === PerformanceQuotesType.PERFORMANCE && (
              <LineGraph
                range={rangePoints}
                points={purchaseAmountPoints}
                animated
                color="#2E7877"
                enableFadeInMask
                indicatorPulsating
                verticalPadding={30}
                panGestureDelay={200}
                lineThickness={2}
                style={{
                  height: 225,
                  marginBottom: 20,
                  marginHorizontal: -24,
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  opacity: 0.25,
                }}
              />
            )}
            <LineGraph
              range={rangePoints}
              points={points}
              animated
              color="#2E7877"
              enablePanGesture
              enableFadeInMask
              enableIndicator
              indicatorPulsating
              verticalPadding={30}
              panGestureDelay={200}
              style={{ height: 225, marginBottom: 20, marginHorizontal: -24 }}
              onGestureStart={() => {
                isPanning.current = true;
              }}
              onGestureEnd={() => {
                isPanning.current = false;
                selectedQuoteShared.value = undefined;
              }}
              onPointSelected={throttle((point: { date: Date; value: number } | undefined) => {
                if (!isPanning.current) return;
                const newQuote =
                  quotes.find(q => Math.abs(q.time - (point?.date.getTime() ?? 0) / 1000) < 1e-5) ?? currentQuote;
                selectedQuoteShared.value = newQuote;
              }, 16)}
            />
          </View>

          <View style={{ alignItems: 'center', marginVertical: 20 }}>
            <View
              style={{
                flexDirection: 'row',
                borderRadius: 12,
                width: '100%',
                justifyContent: 'space-between',
              }}
            >
              {Object.entries(QuoteRange)
                .filter(([k]) => isNaN(+k))
                .sort((a, b) => (a[1] as number) - (b[1] as number))
                .map(([k, v]) => (
                  <Pressable
                    key={k}
                    onPress={() => {
                      selectedQuoteShared.value = undefined;
                      setRange(v as QuoteRange);
                    }}
                    style={[
                      {
                        paddingHorizontal: 18,
                        borderRadius: 22,
                        paddingVertical: 3,
                      },
                      range === v && {
                        borderWidth: 2,
                        borderColor: colors.primary,
                        shadowOffset: {
                          width: 0,
                          height: 3,
                        },
                        shadowOpacity: 0.2,
                        shadowRadius: 6,
                        elevation: 6,
                      },
                    ]}
                  >
                    <ThemedText
                      size={15}
                      type="semi-bold"
                      style={{ color: range === v ? colors.text : colors.tabIconDefault }}
                    >
                      {t(`actor.quotes.radioButton.${k}`)}
                    </ThemedText>
                  </Pressable>
                ))}
            </View>
          </View>
        </>
      )}
      {!isLoading && quotes.length === 0 && (
        <View className="flex-1 justify-center items-center">
          <ThemedText className="text-gray-500 text-lg">{t('actor.quotes.noQuotes')}</ThemedText>
        </View>
      )}
    </Widget>
  );
}
{
  /* <Widget styles={{ container: { height: 200 } }} ready>
        <CartesianChart
          axisOptions={{ lineWidth: 0 }}
          frame={{ lineWidth: 0 }}
          data={quotes.map((quote, index) => ({
            day: new Date(quote.time * 1000),
            price: quote.price,
            date: index,
          }))}
          xKey="date"
          yKeys={['price']}
        >
          {({ points }) => <Line curveType="basis" points={points.price} color="red" strokeWidth={3} />}
        </CartesianChart>
      </Widget> */
}
