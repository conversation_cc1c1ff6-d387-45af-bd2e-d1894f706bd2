import React, { useState } from 'react';

import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';

import SecurityIcon from '@/components/SecurityIcon';
import { Text } from '@/components/base';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import { useThemeColor } from '@/hooks/useThemeColor';
import { ActorService } from '@/services/actor.service';
import { actor } from '@/signals/actor';

import Widget from './Widget';

const MAX_INITIAL = 6;

export default function SecuritiesWidget() {
  const depot = actor.value.depot;
  const { data: performance, isLoading } = usePortfolioQuery({
    queryFn: ActorService.getPerformance,
  });

  const theme = useThemeColor();
  const { t } = useTranslation();

  const entries = performance?.entries || [];
  const totalValue = entries.reduce((sum, entry) => sum + (entry.amount ?? 0), 0);

  const [visibleCount, setVisibleCount] = useState(MAX_INITIAL);
  const visibleEntries = entries.slice(0, visibleCount);

  return (
    <Widget title={t('actor.securitiesWidget.title')} ready={entries.length > 0 && !isLoading}>
      <View>
        {visibleEntries.map((entry, idx) => {
          const percent = totalValue > 0 ? (entry.amount / totalValue) * 100 : 0;
          const dayChange = +(Math.random() * 300 - 150).toFixed(2);
          const perf = +(Math.random() * 30 - 15).toFixed(2);
          const isPositive = dayChange >= 0;
          return (
            <View key={entry.isin} style={{ position: 'relative' }}>
              <View
                style={{
                  position: 'absolute',
                  left: 0,
                  bottom: 0,
                  height: 5,
                  width: '70%',
                  backgroundColor: '#eee',
                  borderRadius: 4,
                  overflow: 'hidden',
                }}
              >
                <View
                  style={{
                    height: '100%',
                    width: `${percent}%`,
                    backgroundColor: '#2E7877',
                    borderRadius: 4,
                  }}
                />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  marginTop: 20,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                  <SecurityIcon
                    isin={entry.isin}
                    country={depot?.securities[entry.isin]?.country!}
                    style={{ width: 40, height: 40, marginRight: 12 }}
                  />
                  <View style={{ flexDirection: 'column', justifyContent: 'center', flex: 1 }}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '700',
                        marginBottom: 2,
                        flexWrap: 'wrap',
                      }}
                      numberOfLines={2}
                      ellipsizeMode="tail"
                    >
                      {entry.name}
                    </Text>
                    <Text style={{ color: theme.muted }}>
                      {t('currency', {
                        amount: {
                          amount: entry.amount,
                          unit: entry.currency ?? 'EUR',
                          options: {
                            notation: 'compact',
                          },
                        },
                      })}
                    </Text>
                  </View>
                </View>
                <View style={{ alignItems: 'flex-end', minWidth: 80, marginTop: 2 }}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: 'bold',
                      color: isPositive ? '#43A047' : '#D34F2F',
                    }}
                  >
                    {dayChange > 0 ? '+' : ''}
                    {dayChange.toFixed(2)} {entry.currency ?? 'EUR'}
                  </Text>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 2 }}>
                    <Text
                      style={{
                        fontSize: 14,
                        color: isPositive ? '#43A047' : '#D34F2F',
                        fontWeight: 'bold',
                        marginRight: 4,
                      }}
                    >
                      <Icon
                        name={isPositive ? 'arrow-up' : 'arrow-down'}
                        size={16}
                        color={isPositive ? '#43A047' : '#D34F2F'}
                      />
                      {t('percent', {
                        value: {
                          value: perf,
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                          signDisplay: 'always',
                        },
                      })}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 18 }}>
                <Text
                  style={{
                    fontSize: 13,
                    color: theme.muted,
                  }}
                >
                  {entry.quantity} ×
                  {t('currency', {
                    amount: {
                      amount: entry.quote,
                      unit: entry.currency,
                    },
                  })}
                </Text>
              </View>
            </View>
          );
        })}

        {entries.length === 0 && (
          <Text style={{ textAlign: 'center', color: '#aaa', marginTop: 20 }}>
            {t('actor.securitiesWidget.noData')}
          </Text>
        )}
        {visibleCount < entries.length && (
          <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 16 }}>
            <Text
              style={{
                color: '#2E7877',
                fontWeight: 'bold',
                fontSize: 16,
                textAlign: 'right',
              }}
              onPress={() => setVisibleCount(v => v + MAX_INITIAL)}
            >
              {t('actor.activitiesWidget.more')}
            </Text>
          </View>
        )}
      </View>
    </Widget>
  );
}
