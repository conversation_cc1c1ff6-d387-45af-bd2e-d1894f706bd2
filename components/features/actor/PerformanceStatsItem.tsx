import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

export interface PerformanceStatsItemProps {
  title: string;
  value: {
    amount: number;
    unit: string;
  };
  changePercentage?: number;
  extraInfo?: React.ReactNode;
  action?: React.ReactNode;
}

export default function PerformanceStatsItem({
  title,
  value,
  changePercentage,
  extraInfo,
  action,
}: PerformanceStatsItemProps) {
  const { t } = useTranslation();
  const { colors, isDark } = useTheme();

  const formatCurrency = (amount: number, unit: string) => {
    return t('currency', {
      amount: {
        amount,
        unit: unit || 'EUR',
      },
    });
  };

  const formatPercentage = (percentage: number) => {
    const sign = percentage >= 0 ? '↑' : '↓';
    return `${sign}${Math.abs(percentage).toFixed(2)}%`;
  };

  return (
    <View>
      <ThemedText style={styles.title}>{title}</ThemedText>
      <ExpoLinearGradient
        colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientContainer}
      >
        <View>
          <ThemedText size={18} type="bold" style={[styles.mainValue, { color: colors.primary }]}>
            {formatCurrency(value.amount, value.unit)}
          </ThemedText>
        </View>
        <View style={styles.rightContainer}>
          {changePercentage !== undefined && (
            <View style={styles.changeContainer}>
              <ThemedText size={14} style={[styles.changeText, { color: colors.primary }]}>
                {formatPercentage(changePercentage)}
              </ThemedText>
              {extraInfo && (
                <ThemedText size={14} textType="muted" style={styles.periodText}>
                  {extraInfo}
                </ThemedText>
              )}
            </View>
          )}
          {action && action}
        </View>
      </ExpoLinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  title: {
    marginBottom: 8,
    lineHeight: 20,
    paddingHorizontal: 16,
  },
  gradientContainer: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginBottom: 8,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  mainValue: {
    letterSpacing: -1,
  },
  rightContainer: {
    alignItems: 'flex-end',
  },
  changeContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 8,
  },
  changeText: {
    fontWeight: '600',
  },
  periodText: {
    lineHeight: 20,
  },
});
