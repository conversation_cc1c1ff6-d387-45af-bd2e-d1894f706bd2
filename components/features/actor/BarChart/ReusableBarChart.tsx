import React, { useState } from 'react';

import { Dimensions, StyleSheet, View } from 'react-native';
import { BarChart } from 'react-native-gifted-charts';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface BarDataPoint {
  value: number;
  label?: string;
  frontColor?: string;
  gradientColor?: string;
  spacing?: number;
  labelWidth?: number;
  labelTextStyle?: object;
  originalDate?: string;
  [key: string]: any; // Allow additional properties
}

export interface ReusableBarChartProps {
  data: BarDataPoint[];
  width?: number;
  height?: number;
  maxValue?: number;
  minValue?: number;
  barWidth?: number;
  spacing?: number;
  color?: string;
  gradientColor?: string;
  isAnimated?: boolean;
  animationDuration?: number;
  showGradient?: boolean;
  hideRules?: boolean;
  hideYAxisText?: boolean;
  hideAxesAndRules?: boolean;
  showValuesAsTopLabel?: boolean;
  scrollToEnd?: boolean;
  disableScroll?: boolean;
  roundedTop?: boolean;
  roundedBottom?: boolean;
  barBorderRadius?: number;
  showScrollIndicator?: boolean;
  noOfSections?: number;
  stepValue?: number;
  onPress?: (item: BarDataPoint, index: number) => void;
  labelWidth?: number;
  xAxisLabelTextStyle?: object;
  containerStyle?: any;
  showValueIndicator?: boolean;
  valueIndicatorColor?: string;
  valueIndicatorBackgroundColor?: string;
  formatValueForIndicator?: (value: number) => string;
  currencySymbol?: string;
  hideValueIndicatorAfter?: number; // milliseconds
}

interface ValueIndicatorProps {
  value: number;
  position: number;
  formatValue: (value: number) => string;
  backgroundColor?: string;
  textColor?: string;
}

const ValueIndicator: React.FC<ValueIndicatorProps> = ({
  value,
  position,
  formatValue,
  backgroundColor = 'rgba(255, 255, 255, 0.95)',
  textColor = '#666',
}) => (
  <View
    style={[
      styles.valueIndicator,
      {
        top: position + 5,
        backgroundColor,
        borderColor: 'rgba(136, 136, 136, 0.3)',
      },
    ]}
  >
    <ThemedText size={11} style={{ color: textColor, fontWeight: '600' }}>
      {formatValue(value)}
    </ThemedText>
  </View>
);

const ReusableBarChart: React.FC<ReusableBarChartProps> = ({
  data,
  width = screenWidth - 40,
  height = 200,
  maxValue,
  minValue,
  barWidth = 8,
  spacing,
  color,
  gradientColor,
  isAnimated = true,
  animationDuration = 1500,
  showGradient = true,
  hideRules = true,
  hideYAxisText = true,
  hideAxesAndRules = true,
  showValuesAsTopLabel = false,
  scrollToEnd = false,
  disableScroll = false,
  roundedTop = true,
  roundedBottom = false,
  barBorderRadius = 2,
  showScrollIndicator = false,
  noOfSections = 4,
  stepValue,
  onPress,
  labelWidth,
  xAxisLabelTextStyle,
  containerStyle,
  showValueIndicator = false,
  valueIndicatorColor = '#ccc',
  valueIndicatorBackgroundColor = 'rgba(255, 255, 255, 0.95)',
  formatValueForIndicator,
  currencySymbol = '$',
  hideValueIndicatorAfter = 4000,
}) => {
  const { colors, isLight, isDark } = useTheme();

  // State for value indicator
  const [showValueDisplay, setShowValueDisplay] = useState(false);
  const [selectedBarValue, setSelectedBarValue] = useState<number | null>(null);
  const [selectedBarPosition, setSelectedBarPosition] = useState<number | null>(null);

  // Calculate dynamic values if not provided
  const chartColor = color ?? colors.primary;
  const chartGradientColor = gradientColor ?? chartColor + '40';
  const calculatedMaxValue = maxValue ?? Math.max(...data.map(item => item.value)) * 1.2;
  const calculatedMinValue = minValue ?? 0;
  const calculatedStepValue = stepValue ?? calculatedMaxValue / noOfSections;
  const calculatedSpacing = spacing ?? (data[0]?.spacing || 45);
  const calculatedLabelWidth = labelWidth ?? (data[0]?.labelWidth || 50);

  // Default format function for value indicator
  const defaultFormatValue = (value: number): string => {
    return `${currencySymbol}${value.toFixed(2)}`;
  };

  const formatValue = formatValueForIndicator ?? defaultFormatValue;

  // Style the data with theme colors if not already styled
  const styledData = data.map((item, index) => ({
    ...item,
    frontColor: item.frontColor || chartColor,
    gradientColor: item.gradientColor || chartGradientColor,
    spacing: index === data.length - 1 ? 0 : (item.spacing ?? calculatedSpacing),
    labelWidth: item.labelWidth ?? calculatedLabelWidth,
  }));

  const handleBarPress = (item: any, index: number) => {
    if (showValueIndicator) {
      setShowValueDisplay(true);
      setSelectedBarValue(item.value);

      // Calculate the vertical position of the horizontal line based on value
      const relativePosition = (item.value / calculatedMaxValue) * height;
      const linePosition = height - relativePosition; // Invert since we measure from top
      setSelectedBarPosition(linePosition);

      // Hide indicators after specified time
      setTimeout(() => {
        setShowValueDisplay(false);
        setSelectedBarValue(null);
        setSelectedBarPosition(null);
      }, hideValueIndicatorAfter);
    }

    // Call the custom onPress handler if provided
    onPress?.(item, index);
  };

  const calculatedXAxisLabelTextStyle = xAxisLabelTextStyle ?? {
    color: colors.primary,
    fontSize: 10,
    textAlign: 'left' as const,
  };

  return (
    <View
      style={[
        {
          position: 'relative',
          minHeight: height + 50,
          width: '100%',
          alignItems: 'stretch',
          paddingHorizontal: 8,
        },
        containerStyle,
      ]}
    >
      <BarChart
        data={styledData}
        width={width}
        height={height}
        barWidth={barWidth}
        spacing={calculatedSpacing}
        roundedTop={roundedTop}
        roundedBottom={roundedBottom}
        hideRules={hideRules}
        hideYAxisText={hideYAxisText}
        hideAxesAndRules={hideAxesAndRules}
        yAxisThickness={0}
        xAxisThickness={0}
        isAnimated={isAnimated}
        animationDuration={animationDuration}
        maxValue={calculatedMaxValue}
        mostNegativeValue={calculatedMinValue}
        frontColor={chartColor}
        gradientColor={chartGradientColor}
        showGradient={showGradient}
        backgroundColor="transparent"
        showValuesAsTopLabel={showValuesAsTopLabel}
        xAxisLabelTextStyle={calculatedXAxisLabelTextStyle}
        labelWidth={calculatedLabelWidth}
        showScrollIndicator={showScrollIndicator}
        scrollToEnd={scrollToEnd}
        disableScroll={disableScroll}
        initialSpacing={0}
        barBorderRadius={barBorderRadius}
        stepValue={calculatedStepValue}
        noOfSections={noOfSections}
        onPress={handleBarPress}
      />

      {/* Horizontal line indicator */}
      {showValueIndicator && showValueDisplay && selectedBarPosition !== null && (
        <View
          style={[
            styles.horizontalLine,
            {
              top: selectedBarPosition + 10, // Offset for padding
              backgroundColor: valueIndicatorColor,
            },
          ]}
        />
      )}

      {/* Value text indicator */}
      {showValueIndicator && showValueDisplay && selectedBarValue !== null && selectedBarPosition !== null && (
        <ValueIndicator
          value={selectedBarValue}
          position={selectedBarPosition}
          formatValue={formatValue}
          backgroundColor={valueIndicatorBackgroundColor}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  horizontalLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    zIndex: 10,
  },
  valueIndicator: {
    position: 'absolute',
    left: 0,
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 6,
    borderWidth: 1,
    zIndex: 11,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});

export default ReusableBarChart;
