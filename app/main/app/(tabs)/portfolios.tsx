import React from 'react';

import { useSignals } from '@preact/signals-react/runtime';
import { Icon } from '@rneui/themed';
import { capitalize } from 'lodash';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';

import { apiDelete } from '@/common/api';
import { showConfirmationDialog } from '@/common/inputDialog';
import { useUserProfile } from '@/common/profile';
import SectionList from '@/components/SectionList';
import { Button, SafeAreaView, ScrollScreen } from '@/components/base';
import PortfolioOnboarding from '@/components/features/on-boarding/PortfolioOnboarding';
import PortfolioConnectModal from '@/components/features/portfolio-import/PortfolioConnectModal';
import { BankParentIcon } from '@/components/features/portfolio-overview/BankParentIcon';
import { useSnackbar } from '@/components/global/Snackbar';
import { ThemedText } from '@/components/global/ThemedText';
import { ModalManager } from '@/components/global/modal';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { isPortfolioConnectOnboardingVisible } from '@/signals/app.signal';

export default function Portfolios() {
  const { t } = useTranslation();
  const { profile, refetch } = useUserProfile();
  const { colors } = useTheme();
  const { showSnackbar } = useSnackbar();
  const [deleteLoadingById, setDeleteLoadingById] = React.useState<Record<string, boolean>>({});

  useSignals();

  const filteredPortfolios = profile.depots.filter(d => !d.isClone);

  const handleDeletePortfolio = async (portfolioId: string) => {
    const confirmed = await showConfirmationDialog(
      t('portfolio.deletePortfolio.title'),
      t('portfolio.deletePortfolio.description'),
    );
    if (confirmed) {
      setDeleteLoadingById(prev => ({ ...prev, [portfolioId]: true }));
      await apiDelete(`/depots/${portfolioId}`)
        .then(refetch)
        .catch(e => {
          showSnackbar(e.message, { type: 'error' });
          setDeleteLoadingById(prev => ({ ...prev, [portfolioId]: false }));
        });
    }
    setDeleteLoadingById(prev => ({ ...prev, [portfolioId]: false }));
  };

  return (
    <ScrollScreen>
      <ThemedText h1 className="mb-5 mx-1.5">
        {t('portfolio.title')}
      </ThemedText>

      {/* <Button
        disabled={isPortfolioConnectOnboardingVisible.value}
        onPress={() => (isPortfolioConnectOnboardingVisible.value = true)}
        containerStyle={{ marginBottom: 10 }}
      >
        Onboarding
      </Button> */}

      {isPortfolioConnectOnboardingVisible.value ? (
        <PortfolioOnboarding />
      ) : !filteredPortfolios.length ? (
        <View className="flex-1 flex gap-5 justify-center items-center">
          <Icon name="block" type="material" size={64} color={colors.tabIconDefault} />
          <ThemedText h3>{t('portfolio.noPortfolios')}</ThemedText>
          <Button onPress={() => ModalManager.showModal(PortfolioConnectModal)}>
            {t('portfolioConnect.bankLogin.title')}
          </Button>
        </View>
      ) : (
        <SectionList
          items={[
            ...filteredPortfolios.map(portfolio => ({
              key: portfolio.id,
              leftIcon: <BankParentIcon bankParent={portfolio.bankType} size={25} />,
              title: (
                <View>
                  <ThemedText h4 className="font-bold line-clamp-1 flex-1 flex-wrap">
                    {capitalize(portfolio.bankName)}
                  </ThemedText>
                  <ThemedText className="line-clamp-1">{portfolio.description || portfolio.number}</ThemedText>
                </View>
              ),
              onRemove: () => handleDeletePortfolio(portfolio.id),
              containerStyle: {
                opacity: deleteLoadingById[portfolio.id] ? 0.5 : 1,
              },
            })),
            {
              title: t('portfolioConnect.bankLogin.title'),
              onPress: () => ModalManager.showModal(PortfolioConnectModal),
              leftIcon: {
                name: 'add',
                type: 'material',
              },
            },
          ]}
        />
      )}
    </ScrollScreen>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingTop: 40,
  },
});
