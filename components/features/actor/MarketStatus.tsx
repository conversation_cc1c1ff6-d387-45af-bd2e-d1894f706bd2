import React, { useRef, useState } from 'react';

import { color } from '@rneui/base';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { Animated, Dimensions, Image, PanResponder, StyleSheet, TouchableOpacity, View } from 'react-native';

import Widget from '@/components/features/actor/Widget';
import { useTheme } from '@/context/ThemeContext';

import { ThemedText } from '../../global/ThemedText';

interface MarketInsight {
  id: string;
  mood: string;
  descriptionKey: string;
  emoji: any;
}

const marketInsights: MarketInsight[] = [
  {
    id: '1',
    mood: 'SHOCK',
    descriptionKey: 'actor.market.mood.shock.description',
    emoji: require('@/assets/images/emoj/boom-emoj.png'),
  },
  {
    id: '2',
    mood: 'BULLISH',
    descriptionKey: 'actor.market.mood.bullish.description',
    emoji: require('@/assets/images/emoj/boom-emoj.png'),
  },
  {
    id: '3',
    mood: 'BEARISH',
    descriptionKey: 'actor.market.mood.bearish.description',
    emoji: require('@/assets/images/emoj/boom-emoj.png'),
  },
  {
    id: '4',
    mood: 'STABLE',
    descriptionKey: 'actor.market.mood.stable.description',
    emoji: require('@/assets/images/emoj/boom-emoj.png'),
  },
];

interface MarketStatusProps {
  onOpenApp?: () => void;
}

const MarketStatus: React.FC<MarketStatusProps> = ({ onOpenApp }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { colors, isLight, isDark } = useTheme();
  const { t } = useTranslation();

  const screenWidth = Dimensions.get('window').width;

  // Animation values
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;

  const currentInsight = marketInsights[currentIndex];

  const animateToNext = (direction: 'left' | 'right') => {
    Animated.parallel([
      Animated.timing(slideAnimation, {
        toValue: direction === 'left' ? -screenWidth : screenWidth,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnimation, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Reset position and update index
      slideAnimation.setValue(direction === 'left' ? screenWidth : -screenWidth);

      // Animate back to center with new content
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  const handleNext = () => {
    const nextIndex = (currentIndex + 1) % marketInsights.length;
    animateToNext('left');
    setCurrentIndex(nextIndex);
  };

  const handlePrevious = () => {
    const prevIndex = (currentIndex - 1 + marketInsights.length) % marketInsights.length;
    animateToNext('right');
    setCurrentIndex(prevIndex);
  };

  const handleDotPress = (index: number) => {
    if (index !== currentIndex) {
      const direction = index > currentIndex ? 'left' : 'right';
      animateToNext(direction);
      setCurrentIndex(index);
    }
  };

  const handleOpenApp = () => {
    if (onOpenApp) {
      onOpenApp();
    } else {
      handleNext();
    }
  };

  // Pan responder for swipe gestures
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to horizontal swipes
      return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 10;
    },
    onPanResponderMove: (evt, gestureState) => {
      // Add real-time visual feedback during swipe
      const { dx } = gestureState;
      const maxSwipe = screenWidth * 0.3; // Limit the drag distance
      const clampedDx = Math.max(-maxSwipe, Math.min(maxSwipe, dx));
      slideAnimation.setValue(clampedDx);

      // Fade slightly during drag
      const fadeValue = 1 - (Math.abs(clampedDx) / maxSwipe) * 0.3;
      fadeAnimation.setValue(fadeValue);
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx } = gestureState;
      const minSwipeDistance = 50;

      if (Math.abs(dx) > minSwipeDistance) {
        if (dx > 0) {
          // Swipe right - go to previous insight
          handlePrevious();
        } else {
          // Swipe left - go to next insight
          handleNext();
        }
      } else {
        // Snap back to center if swipe wasn't far enough
        Animated.parallel([
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(fadeAnimation, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
        ]).start();
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      borderBottomLeftRadius: 30,
      borderBottomRightRadius: 30,
      backgroundColor: colors.background,
      position: 'relative',
      paddingBottom: 20, // Add space for the button that extends outside
    },
    fullBackground: {
      flex: 1,
      borderBottomLeftRadius: 30,
      borderBottomRightRadius: 30,
      overflow: 'hidden', // Keep gradient clipped to rounded corners
    },
    title: {
      textAlign: 'center',
    },
    content: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingTop: 20,
    },
    animatedContent: {
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },

    emoji: {
      width: 213,
      height: 122,
      marginTop: 32,
      resizeMode: 'contain',
    },
    description: {
      textAlign: 'center',
      marginBottom: 8,
      color: colors.primary,
    },
    mood: {
      textAlign: 'center',
      marginBottom: 10,
      color: colors.primary,
    },
    navigation: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 8,
      paddingBottom: 30,
    },
    dot: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    activeDot: {
      backgroundColor: colors.primary,
    },
    inactiveDot: {
      backgroundColor: colors.border,
      opacity: 0.3,
    },

    button: {
      position: 'absolute',
      left: '50%',
      marginLeft: -90,
      bottom: -20, // Position it so half is inside container, half outside
      zIndex: 999,
      backgroundColor: colors.primary,
      borderRadius: 25,
      height: 40,
      width: 180,
      alignItems: 'center',
      justifyContent: 'center',
    },
    buttonText: {
      color: 'white',
    },
  });

  return (
    <Widget title={t('actor.market.insights')} ready={true} extraMargin>
      <View style={styles.container}>
        {/* Gradient Background covering entire widget */}
        <ExpoLinearGradient
          colors={isDark ? ['#1d2b39', '#1a202c', '#111111'] : ['#f7fafc', '#ffffff', '#FFFFFF']}
          locations={[0, 0.4952, 1]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.fullBackground}
          {...panResponder.panHandlers}
        >
          {/* Content Area */}
          <View style={styles.content}>
            <Animated.View
              style={[
                styles.animatedContent,
                {
                  transform: [{ translateX: slideAnimation }],
                  opacity: fadeAnimation,
                },
              ]}
            >
              {/* Emoji */}
              <View>
                <Image source={currentInsight.emoji} style={styles.emoji} />
              </View>

              {/* Market Mood Description */}
              <ThemedText style={styles.description} type="outfit-semi-bold" size={12}>
                {t(currentInsight.descriptionKey)}
              </ThemedText>

              {/* Market Mood */}
              <ThemedText style={styles.mood} size={28} type="outfit-bold">
                {t(`actor.market.mood.${currentInsight.mood.toLowerCase()}.title`)}
              </ThemedText>
            </Animated.View>
          </View>

          {/* Navigation Dots */}
          <View style={styles.navigation}>
            {marketInsights.map((_, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleDotPress(index)}
                style={[styles.dot, index === currentIndex ? styles.activeDot : styles.inactiveDot]}
              />
            ))}
          </View>
        </ExpoLinearGradient>

        {/* Button */}
        <TouchableOpacity style={styles.button} onPress={handleOpenApp} activeOpacity={0.8}>
          <ThemedText style={styles.buttonText} type="semi-bold" size={16}>
            {t('actor.market.openApp')}
          </ThemedText>
        </TouchableOpacity>
      </View>
    </Widget>
  );
};

export default MarketStatus;
