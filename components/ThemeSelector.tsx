import React from 'react';

import { useTranslation } from 'react-i18next';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { AppTheme } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

const THEME_OPTIONS: { labelKey: string; value: AppTheme | 'system' }[] = [
  { labelKey: 'settings.themeSettings.systemDefault', value: 'system' },
  { labelKey: 'settings.themeSettings.lightOrange', value: 'lightOrange' },
  { labelKey: 'settings.themeSettings.lightRed', value: 'lightRed' },
  { labelKey: 'settings.themeSettings.lightPurple', value: 'lightPurple' },
  { labelKey: 'settings.themeSettings.lightBlue', value: 'lightBlue' },
  { labelKey: 'settings.themeSettings.lightGreen', value: 'lightGreen' },
  { labelKey: 'settings.themeSettings.darkOrange', value: 'darkOrange' },
  { labelKey: 'settings.themeSettings.darkRed', value: 'darkRed' },
  { labelKey: 'settings.themeSettings.darkPurple', value: 'darkPurple' },
  { labelKey: 'settings.themeSettings.darkBlue', value: 'darkBlue' },
  { labelKey: 'settings.themeSettings.darkGreen', value: 'darkGreen' },
];

export function ThemeSelector() {
  const { theme, setTheme, currentTheme, colors } = useTheme();
  const { t } = useTranslation();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedText size={24} type="bold" style={styles.title}>
        {t('settings.themeSettings.chooseTheme')}
      </ThemedText>
      <ThemedText size={14} style={styles.subtitle}>
        {t('settings.themeSettings.current')}: {currentTheme} ({t('settings.themeSettings.selected')}: {theme})
      </ThemedText>

      <ScrollView style={styles.scrollView}>
        {THEME_OPTIONS.map(option => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.themeOption,
              {
                backgroundColor: theme === option.value ? colors.primary : colors.secondary,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setTheme(option.value)}
          >
            <ThemedText
              size={16}
              lightColor={theme === option.value ? colors.background : colors.text}
              darkColor={theme === option.value ? colors.background : colors.text}
              style={[
                styles.themeOptionText,
                {
                  fontWeight: theme === option.value ? 'bold' : 'normal',
                },
              ]}
            >
              {t(option.labelKey)}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={[styles.previewContainer, { borderColor: colors.border }]}>
        <ThemedText size={18} type="bold" style={styles.previewTitle}>
          {t('settings.themeSettings.preview')}
        </ThemedText>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.primary }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            {t('settings.themeSettings.primary')}
          </ThemedText>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.secondary }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            {t('settings.themeSettings.secondary')}
          </ThemedText>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.text }]} />
          <ThemedText size={14} style={styles.colorLabel}>
            {t('settings.themeSettings.text')}
          </ThemedText>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 20,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
    marginBottom: 20,
  },
  themeOption: {
    padding: 15,
    marginVertical: 5,
    borderRadius: 8,
    borderWidth: 1,
  },
  themeOptionText: {
    textAlign: 'center',
  },
  previewContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  previewTitle: {
    marginBottom: 10,
  },
  colorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  colorSwatch: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 10,
  },
  colorLabel: {},
});
