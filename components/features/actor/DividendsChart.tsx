import React, { useMemo, useState } from 'react';

import { useTranslation } from 'react-i18next';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import { ReusableBarChart } from '@/components/features/actor/BarChart';
import type { BarDataPoint } from '@/components/features/actor/BarChart';
import Widget from '@/components/features/actor/Widget';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { CompanyDividendData, CompanyDividendHistory } from '@/types/actor-api.types';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 40; // Reduced margin for better use of space

// Generate dividend data from actual data only with error handling (no period logic)
const generateDividendData = (dividendData: CompanyDividendData | undefined, colors: any): BarDataPoint[] => {
  try {
    if (dividendData?.dividends && dividendData.dividends.length > 0) {
      const dividends = dividendData.dividends;
      // Show all dividend payments as individual bars
      return dividends.map((dividend, i) => {
        const date = new Date(dividend.date);
        const label = isNaN(date.getTime()) ? 'Invalid' : date.getFullYear().toString();
        return {
          value: dividend.yield?.amount || 0,
          label,
          frontColor: colors.primary,
          gradientColor: colors.primary + '40',
          spacing: i === dividends.length - 1 ? 0 : 30,
          labelWidth: 35,
        };
      });
    }
    return [];
  } catch (error) {
    return [];
  }
};

interface DividendsChartProps {
  dividendData?: CompanyDividendData;
  isLoading?: boolean;
}

// Render content function - no period logic, just show chart or fallback
const renderContent = (
  dividendData: CompanyDividendData | undefined,
  isLoading: boolean,
  chartData: BarDataPoint[],
  maxValue: number,
  chartWidth: number,
  currencyUnit: string,
  colors: any,
  t: (key: string) => string,
) => {
  const hasValidChartData = Boolean(chartData && Array.isArray(chartData) && chartData.length > 0);
  const shouldShowChart = hasValidChartData && !isLoading;

  if (isLoading) {
    return (
      <View style={{ padding: 20, alignItems: 'center', minHeight: 200 }}>
        <ThemedText size={16} style={{ textAlign: 'center', opacity: 0.7 }}>
          {t('actor.dividends.loadingData')}
        </ThemedText>
      </View>
    );
  }

  if (!shouldShowChart) {
    return (
      <View style={{ padding: 20, alignItems: 'center', minHeight: 200 }}>
        <ThemedText size={16} style={{ textAlign: 'center', opacity: 0.7 }}>
          {t('actor.dividends.noData')}
        </ThemedText>
      </View>
    );
  }

  return (
    <View style={styles.chartContainerWrapper}>
      <View style={[styles.chartContainer, { position: 'relative', minHeight: 250, marginHorizontal: 10 }]}>
        <ReusableBarChart
          key={`dividends-chart-${chartData.length}`}
          data={chartData}
          width={chartWidth}
          height={200}
          barWidth={8}
          spacing={chartData[0]?.spacing || 45}
          maxValue={maxValue}
          color={colors.primary}
          gradientColor={colors.primary + '40'}
          showValueIndicator={true}
          currencySymbol={currencyUnit === 'EUR' ? '€' : '$'}
          formatValueForIndicator={(value: number) => `${currencyUnit === 'EUR' ? '€' : '$'}${value.toFixed(2)}`}
          onPress={(item: any, index: number) => {
            // Handle bar press if needed
          }}
        />
      </View>
    </View>
  );
};

const DividendsChart: React.FC<DividendsChartProps> = ({ dividendData, isLoading = false }) => {
  const { colors } = useTheme();
  const { t } = useTranslation();

  // Compute chart data and related values directly in useMemo
  const { chartData, maxValue } = useMemo(() => {
    try {
      const newData = generateDividendData(dividendData, colors);
      let calculatedMaxValue = 1;
      if (newData.length > 0) {
        const validValues = newData.map(item => item.value).filter(value => !isNaN(value) && value >= 0);
        if (validValues.length > 0) {
          const max = Math.max(...validValues);
          // Set maxValue to max (or max*1.05 for a small margin at the top)
          calculatedMaxValue = max > 0 ? max * 1.05 : 1;
        }
      }
      return { chartData: newData, maxValue: calculatedMaxValue };
    } catch (error) {
      return { chartData: [], maxValue: 1 };
    }
  }, [dividendData, colors.primary]);

  const currencyUnit = dividendData?.dividends?.[0]?.yield.unit || 'EUR';

  if (isLoading) {
    return (
      <Widget title={t('actor.dividends.title')} ready={false} subtitle={t('actor.dividends.loadingData')}>
        <View style={{ padding: 20, alignItems: 'center', minHeight: 200, flex: 1 }}>
          <ThemedText size={16} style={{ textAlign: 'center', opacity: 0.7 }}>
            {t('actor.dividends.loadingData')}
          </ThemedText>
        </View>
      </Widget>
    );
  }

  return (
    <Widget title={t('actor.dividends.title')} ready={!isLoading} subtitle={t('actor.dividends.swipeChart')}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {renderContent(dividendData, isLoading, chartData, maxValue, chartWidth, currencyUnit, colors, t)}
      </ScrollView>
    </Widget>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  header: {
    marginBottom: 36,
  },
  subtitleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  statsContainer: {
    gap: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chartContainerWrapper: {
    width: '100%',
    paddingHorizontal: 0,
  },

  chartContainer: {
    width: '100%',
    alignItems: 'stretch',
    paddingHorizontal: 8,
  },

  periodSelector: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  periodButtonsContainer: {
    flexDirection: 'row',
    borderRadius: 28,
    width: '100%',
    justifyContent: 'space-between',
  },
});

export default DividendsChart;
