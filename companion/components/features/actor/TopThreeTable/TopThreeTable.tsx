import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface TopThreeTableProps {
  data: Array<{ id: string; name: string; value: string }>;
  title: string;
}

const TopThreeTable: React.FC<TopThreeTableProps> = ({ data, title }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      {data.map(item => (
        <View key={item.id} style={styles.row}>
          <Text style={styles.cell}>{item.name}</Text>
          <Text style={styles.cell}>{item.value}</Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  cell: {
    fontSize: 16,
  },
});

export default TopThreeTable;