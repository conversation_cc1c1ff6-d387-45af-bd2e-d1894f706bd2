import React, { useMemo, useRef, useState } from 'react';

import { Dimensions, View } from 'react-native';
import { LineChart, lineDataItem } from 'react-native-gifted-charts';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface LineDataPoint extends lineDataItem {
  value: number;
}

export interface LineChartSeries {
  data: LineDataPoint[];
  color?: string;
  thickness?: number;
  areaChart?: boolean;
  startFillColor?: string;
  endFillColor?: string;
  startOpacity?: number;
  endOpacity?: number;
  hideDataPoints?: boolean;
}

export interface ReusableLineChartProps {
  series: LineChartSeries[];
  height?: number;
  width?: number;
  maxValue?: number;
  minValue?: number;
  spacing?: number;
  curved?: boolean;
  isAnimated?: boolean;
  animationDuration?: number;
  disableScroll?: boolean;
  showPointer?: boolean;
  formatValueForPointer?: (items?: LineDataPoint[]) => string | undefined;
  pointerLabelComponent?: (items?: LineDataPoint[]) => React.ReactNode | string | undefined;
  containerStyle?: any;
}

const ReusableLineChart = ({
  series,
  height = 160,
  width = screenWidth,
  maxValue,
  minValue,
  spacing,
  curved = true,
  isAnimated = true,
  animationDuration = 800,
  disableScroll = true,
  showPointer = true,
  formatValueForPointer,
  pointerLabelComponent,
  containerStyle,
}: ReusableLineChartProps) => {
  const { colors } = useTheme();
  const [actualContainerWidth, setActualContainerWidth] = useState<number>(width - 32);
  const viewRef = useRef<View>(null);

  const limitedSeries = series.slice(0, 5);
  const dataArr = limitedSeries[0]?.data ?? [];

  // Fallback for missing series
  const calculatedSpacing = useMemo(() => {
    if (!actualContainerWidth || dataArr.length === 0) return undefined;
    return spacing ?? actualContainerWidth / dataArr.length;
  }, [actualContainerWidth, dataArr.length, spacing]);

  // Calculate max and min values from all series
  const allValues = limitedSeries.flatMap(s => s.data.map(item => item.value)).filter(value => isFinite(value));

  // Make minValue a bit lower for beauty, but not below zero if all data is positive
  const calculatedMaxValue = maxValue ?? (allValues.length > 0 ? Math.max(...allValues) * 1.1 : 1000);
  const calculatedMinValue = minValue ?? (allValues.length > 0 ? Math.min(...allValues) * 0.9 : 0);

  // Dynamically build props for up to 5 series
  const chartProps: Record<string, any> = {};
  for (let i = 0; i < limitedSeries.length; i++) {
    const s = limitedSeries[i];
    const suffix = i === 0 ? '' : String(i + 1);
    chartProps['data' + suffix] = s.data;
    chartProps['color' + suffix] =
      s.color ?? [colors.primary, colors.secondary ?? '#FF6B6B', '#4ECDC4', '#FFD166', '#118AB2'][i % 5];
    chartProps['thickness' + suffix] =
      s.thickness ?? (i === 0 ? 2.5 : (chartProps['thickness' + (i === 1 ? '' : String(i))] ?? 2.5));
    chartProps['areaChart' + suffix] = s.areaChart ?? i === 0;
    chartProps['startFillColor' + suffix] = s.startFillColor ?? (i === 0 ? colors.linearGradient[0] + '50' : undefined);
    chartProps['endFillColor' + suffix] = s.endFillColor ?? (i === 0 ? colors.linearGradient[1] : undefined);
    chartProps['startOpacity' + suffix] = s.startOpacity ?? (i === 0 ? 0.7 : 0);
    chartProps['endOpacity' + suffix] = s.endOpacity ?? 0;
    chartProps['hideDataPoints' + suffix] =
      s.hideDataPoints ?? (i === 0 ? true : (chartProps['hideDataPoints' + (i === 1 ? '' : String(i))] ?? true));
  }
  // For pointer config, use the color of the first series
  const chartColor = chartProps['color'] ?? colors.primary;
  // Default format function for pointer
  const defaultFormatValue = (value: LineDataPoint[]): string => {
    return Number(value?.[0]?.value ?? 0).toFixed(2);
  };

  const formatValue = formatValueForPointer ?? defaultFormatValue;

  // Default pointer label component
  const defaultPointerLabelComponent = (items: any) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: colors.border,
          paddingHorizontal: 8,
          paddingVertical: 4,
        }}
      >
        <ThemedText size={12} type="semi-bold" style={{ color: colors.primary }}>
          {formatValue(items)}
        </ThemedText>
      </View>
    );
  };

  const pointerComponent = pointerLabelComponent ?? defaultPointerLabelComponent;

  const pointerConfig = showPointer
    ? {
        pointerStripHeight: height,
        pointerStripColor: chartColor + '40',
        pointerStripWidth: 1,
        pointerColor: chartColor,
        radius: 6,
        pointerLabelWidth: 120,
        pointerLabelHeight: 90,
        activatePointersOnLongPress: false,
        autoAdjustPointerLabelPosition: true,
        shiftPointerLabelX: 0,
        shiftPointerLabelY: 0,
        pointerVanishDelay: 3000,
        pointerLabelComponent: pointerComponent,
      }
    : undefined;

  viewRef?.current?.measureInWindow((...dimensions) => {
    setActualContainerWidth(dimensions[2]);
  });

  return (
    <View
      style={[
        {
          overflow: 'hidden',
          width: '100%',
          position: 'relative',
          height: height + 20,
        },
        containerStyle,
      ]}
      ref={viewRef}
    >
      <LineChart
        // width={width}
        height={height}
        spacing={calculatedSpacing}
        initialSpacing={0}
        endSpacing={0}
        yAxisLabelWidth={0}
        yAxisOffset={calculatedMinValue}
        showScrollIndicator={false}
        hideRules
        hideYAxisText
        hideAxesAndRules
        yAxisThickness={0}
        xAxisThickness={0}
        isAnimated={isAnimated}
        animationDuration={animationDuration}
        curved={curved}
        pointerConfig={pointerConfig}
        disableScroll={disableScroll}
        {...chartProps}
      />
    </View>
  );
};

export default ReusableLineChart;
