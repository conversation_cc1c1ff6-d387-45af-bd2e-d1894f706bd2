import React from 'react';

import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';

interface OnboardingPageHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
}

export const OnboardingPageHeader: React.FC<OnboardingPageHeaderProps> = ({ title, subtitle, description }) => {
  return (
    <View style={styles.container}>
      <ThemedText size={32} type="bold" style={styles.title}>
        {title}
      </ThemedText>
      {!!subtitle && (
        <ThemedText size={20} type="semi-bold" style={styles.subtitle}>
          {subtitle}
        </ThemedText>
      )}
      {!!description && (
        <ThemedText size={18} style={styles.description}>
          {description}
        </ThemedText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    alignItems: 'center',
    paddingHorizontal: 24,
    width: '100%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    textAlign: 'center',
  },
});
