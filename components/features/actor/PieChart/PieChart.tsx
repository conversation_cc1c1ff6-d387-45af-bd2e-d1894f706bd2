import { useEffect } from 'react';

import { PieChart as PieChartBase, pieDataItem } from 'react-native-gifted-charts';

import { useTheme } from '@/context/ThemeContext';

export interface PieDataItem extends pieDataItem {
  id: string;
}

interface PieChartProps<T> {
  data: T[];
  centerLabelComponent: () => JSX.Element;
  selectedSegment: T | null;
  setSelectedSegment: (segment: T | null) => void;
  variant?: 'donut' | 'filled'; // 'donut' = current with inner circle, 'filled' = without inner circle
}

export default function PieChart<Datum extends pieDataItem>({
  data,
  centerLabelComponent,
  selectedSegment,
  setSelectedSegment,
  variant = 'donut',
}: PieChartProps<Datum>) {
  const { colors, isLight, isDark } = useTheme();

  const updateFocus = (selected: Datum | null) => {
    data.forEach(segment => {
      segment.focused = selected ? segment === selected : false;
    });
  };

  useEffect(() => {
    updateFocus(selectedSegment);
  }, [selectedSegment]);

  // For this widget, if variant is 'donut', make the inner circle and outer circle look like a single solid circle
  const isDonut = variant === 'donut';
  const chartRadius = 130;
  // If donut, set innerRadius to 0 and innerCircleColor to match the main segment color (first or selected)
  let chartInnerRadius = 0;
  let chartInnerCircleColor = colors.background;
  if (isDonut) {
    // Use selected segment color or first segment color, fallback to theme color
    const mainColor = selectedSegment?.color || data[0]?.color || colors.secondary;
    chartInnerCircleColor = mainColor;
    chartInnerRadius = 0;
  } else {
    chartInnerRadius = 90;
    chartInnerCircleColor = colors.background;
  }

  return (
    <PieChartBase
      data={data}
      showGradient
      extraRadius={7}
      focusOnPress
      inwardExtraLengthForFocused={3}
      animationDuration={500}
      radius={chartRadius}
      innerRadius={chartInnerRadius}
      strokeWidth={0}
      strokeColor={undefined}
      innerCircleColor={chartInnerCircleColor}
      focusedPieIndex={selectedSegment ? data.indexOf(selectedSegment) : -1}
      centerLabelComponent={centerLabelComponent}
      onPress={(segment: Datum) => setSelectedSegment(segment?.focused ? null : (segment ?? null))}
    />
  );
}
