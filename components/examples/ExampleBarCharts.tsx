import React from 'react';

import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';

import { BarDataPoint, ReusableBarChart } from '../features/actor/BarChart';

/**
 * Example usage of the ReusableBarChart component
 * This file demonstrates various ways to use the bar chart
 */

const ExampleBarCharts: React.FC = () => {
  // Example 1: Simple revenue data
  const revenueData: BarDataPoint[] = [
    { value: 1000, label: 'Jan' },
    { value: 1200, label: 'Feb' },
    { value: 1100, label: 'Mar' },
    { value: 1400, label: 'Apr' },
    { value: 1600, label: 'May' },
    { value: 1800, label: 'Jun' },
    { value: 2000, label: 'Jul' },
  ];

  // Example 2: Dividend data with custom styling
  const dividendData: BarDataPoint[] = [
    { value: 2.5, label: 'Q1', frontColor: '#4CAF50' },
    { value: 2.8, label: 'Q2', frontColor: '#2196F3' },
    { value: 3.1, label: 'Q3', frontColor: '#FF9800' },
    { value: 3.5, label: 'Q4', frontColor: '#9C27B0' },
  ];

  // Example 3: Monthly data with gradient
  const monthlyData: BarDataPoint[] = [
    { value: 150, label: 'Jan' },
    { value: 180, label: 'Feb' },
    { value: 220, label: 'Mar' },
    { value: 195, label: 'Apr' },
    { value: 250, label: 'May' },
    { value: 280, label: 'Jun' },
  ];

  const formatCurrency = (value: number): string => `$${value.toFixed(2)}`;
  const formatEuro = (value: number): string => `€${value.toFixed(2)}`;

  return (
    <View style={styles.container}>
      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Simple Revenue Chart
        </ThemedText>
        <ReusableBarChart
          data={revenueData}
          height={120}
          color="#4CAF50"
          formatValueForIndicator={formatCurrency}
          showValueIndicator={true}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Quarterly Dividends (Custom Colors)
        </ThemedText>
        <ReusableBarChart
          data={dividendData}
          height={120}
          showGradient={false}
          showValueIndicator={true}
          currencySymbol="$"
          barWidth={12}
          spacing={60}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Monthly Performance (with Gradient)
        </ThemedText>
        <ReusableBarChart
          data={monthlyData}
          height={120}
          color="#2196F3"
          showGradient={true}
          showValueIndicator={true}
          formatValueForIndicator={formatEuro}
          currencySymbol="€"
          animationDuration={2000}
        />
      </View>

      <View style={styles.chartSection}>
        <ThemedText type="semi-bold" size={16} style={styles.title}>
          Scrollable Chart (More Data Points)
        </ThemedText>
        <ReusableBarChart
          data={Array.from({ length: 20 }, (_, i) => ({
            value: Math.random() * 100 + 50,
            label: `Week ${i + 1}`,
          }))}
          height={120}
          color="#FF5722"
          showValueIndicator={true}
          disableScroll={false}
          barWidth={6}
          spacing={25}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  chartSection: {
    marginBottom: 32,
  },
  title: {
    marginBottom: 12,
  },
});

export default ExampleBarCharts;
