import React from 'react';

import { DimensionValue, TouchableOpacity, TouchableOpacityProps } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

import { ThemedText } from './ThemedText';

interface ThemedButtonProps extends TouchableOpacityProps {
  title: string;
  width?: DimensionValue;
  variant?: 'default' | 'primary' | 'secondary';
}

export const ThemedButton: React.FC<ThemedButtonProps> = ({
  title,
  width = '47%',
  variant = 'default',
  style,
  ...props
}) => {
  const { colors, isLight, isDark } = useTheme();

  const buttonStyle = {
    width,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
  };

  return (
    <TouchableOpacity style={[buttonStyle, style]} {...props}>
      <ThemedText size={22} type="outfit-semi-bold" style={{ color: colors.text, textAlign: 'center' }}>
        {title}
      </ThemedText>
    </TouchableOpacity>
  );
};
