import React from 'react';

import { FlashList, ListRenderItemInfo } from '@shopify/flash-list';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { ThemedView } from '@/components/global/ThemedView';
import { useTheme } from '@/context/ThemeContext';

export interface GenericTableProps {
  headers: Array<{ key: string; label: string; flex?: number; align?: 'left' | 'center' | 'right' } | string>;
  rows: Array<{ [key: string]: React.ReactNode } | React.ReactNode[]>;
  style?: object;
  maxItemsBeforeScroll?: number; // New prop to control when to switch to scrollable
}

const GenericTable: React.FC<GenericTableProps> = ({
  headers,
  rows,
  style,
  maxItemsBeforeScroll = 6, // Default threshold
}) => {
  const { colors, isDark } = useTheme();

  // Normalize headers to objects with automatic alignment based on position
  const normalizedHeaders = headers.map((h, index) => {
    const baseHeader = typeof h === 'string' ? { key: h, label: h, flex: 1 } : { flex: 1, ...h };

    // Auto-assign alignment based on column count and position if not explicitly set
    if (!baseHeader.align) {
      if (headers.length === 2) {
        // 2 columns: first = flex-start, last = flex-end
        baseHeader.align = index === 0 ? 'left' : 'right';
      } else if (headers.length === 3) {
        // 3 columns: first = flex-start, middle = center, last = flex-end
        baseHeader.align = index === 0 ? 'left' : index === 1 ? 'center' : 'right';
      } else {
        // Default to left for other cases
        baseHeader.align = 'left';
      }
    }

    return baseHeader;
  });

  // Get alignment style based on align prop
  const getAlignmentStyle = (align: 'left' | 'center' | 'right') => {
    switch (align) {
      case 'center':
        return { alignItems: 'center' as const };
      case 'right':
        return { alignItems: 'flex-end' as const };
      default:
        return { alignItems: 'flex-start' as const };
    }
  };

  // Calculate estimated item size (header + padding + margins)
  const estimatedRowHeight = 60; // 40 minHeight + 8*2 padding + 4 margin
  const shouldAutoSize = rows.length <= maxItemsBeforeScroll;
  const autoHeight = shouldAutoSize ? Math.max(rows.length * estimatedRowHeight, 120) : undefined;

  return (
    <ThemedView useGradient={false} style={[styles.container, { backgroundColor: colors.background }, style]}>
      {/* Table Header */}
      <View style={styles.headerContainer}>
        <ExpoLinearGradient
          colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.headerRow}
        >
          {normalizedHeaders.map((header, idx) => (
            <View
              key={header.key || idx}
              style={[styles.headerCell, { flex: header.flex ?? 1 }, getAlignmentStyle(header.align || 'left')]}
            >
              <ThemedText size={14} type="semi-bold">
                {header.label}
              </ThemedText>
            </View>
          ))}
        </ExpoLinearGradient>
      </View>

      {/* Table Rows with FlashList */}
      <View
        style={[
          styles.listWrapper,
          shouldAutoSize && autoHeight ? { height: autoHeight, minHeight: 120 } : { flex: 1, minHeight: 200 },
        ]}
      >
        <FlashList
          estimatedItemSize={estimatedRowHeight}
          data={rows}
          keyExtractor={(_item: (typeof rows)[number], index: number) => index.toString()}
          renderItem={({ item: row, index: rowIdx }: ListRenderItemInfo<(typeof rows)[number]>) => (
            <View key={rowIdx} style={styles.rowContainer}>
              {Array.isArray(row)
                ? row.map((cell, cellIdx) => (
                    <View
                      key={cellIdx}
                      style={[
                        styles.cell,
                        { flex: normalizedHeaders[cellIdx]?.flex ?? 1 },
                        getAlignmentStyle(normalizedHeaders[cellIdx]?.align || 'left'),
                      ]}
                    >
                      {typeof cell === 'string' || typeof cell === 'number' ? (
                        <ThemedText size={14}>{cell}</ThemedText>
                      ) : (
                        cell
                      )}
                    </View>
                  ))
                : normalizedHeaders.map((header, cellIdx) => (
                    <View
                      key={header.key || cellIdx}
                      style={[styles.cell, { flex: header.flex ?? 1 }, getAlignmentStyle(header.align || 'left')]}
                    >
                      {typeof row[header.key] === 'string' || typeof row[header.key] === 'number' ? (
                        <ThemedText size={14}>{row[header.key]}</ThemedText>
                      ) : (
                        row[header.key]
                      )}
                    </View>
                  ))}
            </View>
          )}
          scrollEnabled={!shouldAutoSize}
          showsVerticalScrollIndicator={!shouldAutoSize}
        />
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    shadowColor: '#000',
    marginBottom: 24,
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  headerRow: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  headerCell: {
    flex: 1,
  },
  listWrapper: {
    // This wrapper allows us to control the height of FlashList
    width: '100%', // Ensure full width
  },
  rowContainer: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 4,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  cell: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 40,
  },
});

export default GenericTable;
