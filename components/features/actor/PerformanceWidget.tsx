import React from 'react';

import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';

import { useUserProfile } from '@/common/profile';
import ClaimButton from '@/components/features/actor/ClaimButton';
import PerformanceStatsItem from '@/components/features/actor/PerformanceStatsItem';
import Widget from '@/components/features/actor/Widget';
import { ThemedView } from '@/components/global/ThemedView';
import { useTheme } from '@/context/ThemeContext';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import { ActorService } from '@/services/actor.service';
import { actor } from '@/signals/actor';

interface PerformanceWidgetProps {
  onClaimPress?: () => void;
}

export default function PerformanceWidget({ onClaimPress }: PerformanceWidgetProps) {
  const { t } = useTranslation();
  const { colors } = useTheme();
  const depot = actor.value.depot;

  const { data: performance, isLoading: performanceLoading } = usePortfolioQuery({
    queryFn: ActorService.getPerformance,
  });

  const { data: portfolioStats, isLoading: statsLoading } = usePortfolioQuery({
    queryFn: ActorService.getPortfolioStats,
  });

  const currency = useUserProfile().profile?.flags.currency;
  const loading = performanceLoading || statsLoading;

  // Calculate performance change percentage
  const calculatePerformanceChange = () => {
    if (!performance?.entries || performance.entries.length === 0) return 0;

    const totalPerformance = performance.entries.reduce((sum, entry) => sum + entry.performance, 0);
    const totalInvestment = performance.totalAmount - totalPerformance;

    return totalInvestment > 0 ? (totalPerformance / totalInvestment) * 100 : 0;
  };

  // Calculate dividend change percentage
  const getDividendChangePercentage = () => {
    return portfolioStats?.grossDividends?.yearChange || 0;
  };

  const performanceItems = [
    {
      title: t('actor.portfolioStats.totalAssets'),
      value: {
        amount: performance?.totalAmount ?? 0,
        unit: currency ?? 'EUR',
      },
      changePercentage: calculatePerformanceChange(),
      extraInfo: t('actor.performance.over12Months'),
    },
    {
      title: t('actor.portfolioStats.grossDividendThisYear'),
      value: {
        amount: portfolioStats?.grossDividends?.amount || 0,
        unit: portfolioStats?.grossDividends?.unit ?? currency ?? 'EUR',
      },
      changePercentage: getDividendChangePercentage(),
      extraInfo: t('actor.performance.over12Months'),
    },
    {
      title: t('actor.portfolioStats.totalRefundableWithholdingTax'),
      value: {
        amount: depot?.totalRefundableAmount?.amount ?? 0,
        unit: currency ?? 'EUR',
      },
      action: (
        <ClaimButton
          onPress={onClaimPress}
          disabled={!depot?.totalRefundableAmount?.amount || depot.totalRefundableAmount.amount <= 0}
        />
      ),
    },
  ];

  return (
    <Widget isExpandable title={t('actor.performance.title', 'Performance overview')} ready={!loading && !!performance}>
      <ThemedView useGradient={false} style={[styles.container, { backgroundColor: colors.background }]}>
        {performanceItems.map((item, index) => (
          <PerformanceStatsItem
            key={`${item.title}-${index}`}
            title={item.title}
            value={item.value}
            changePercentage={item.changePercentage}
            extraInfo={item.extraInfo}
            action={item.action}
          />
        ))}
      </ThemedView>
    </Widget>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
  },
});
