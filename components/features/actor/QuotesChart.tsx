import React, { useMemo, useState } from 'react';

import { useSignals } from '@preact/signals-react/runtime';
import { useTranslation } from 'react-i18next';
import { Dimensions, StyleSheet, TouchableOpacity, View } from 'react-native';

import Widget from '@/components/features/actor/Widget';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { updateActorSettingsOptimistically } from '@/signals/actions/actor.actions';
import { actor } from '@/signals/actor';
import { ExtendedQuote, PerformanceQuotesType, QuoteRange } from '@/types/actor-api.types';

import ReusableLineChart, { LineDataPoint } from './LineChart/ReusableLineChart';

const { width: screenWidth } = Dimensions.get('window');

interface QuotesChartProps {
  title: string;
  queryKey: (range: QuoteRange) => string[];
  queryFn: (range: QuoteRange) => Promise<ExtendedQuote[]>;
  useQuery: (options: { queryFn: () => Promise<ExtendedQuote[]>; queryKey: string[] }) => {
    data: ExtendedQuote[] | undefined;
    isLoading: boolean;
  };
  noDataText: string;
  periodLabels?: Partial<Record<QuoteRange, string>>;
  performanceTypeLabels?: Partial<Record<PerformanceQuotesType, string>>;
}

const QuotesChart: React.FC<QuotesChartProps> = ({ title, queryKey, queryFn, useQuery, noDataText }) => {
  useSignals(); // Ensure reactivity to actor signal changes
  const { t } = useTranslation();
  const { colors: themeColors } = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState<QuoteRange>(QuoteRange.Y);

  const range = selectedPeriod;
  const { data: quotes = [], isLoading } = useQuery({
    queryFn: () => queryFn(range),
    queryKey: queryKey(range),
  });

  const chartHeight = 160;
  const currentQuote = quotes.at(-1);

  const selectedPerformanceType =
    actor.value.settings?.performanceQuotesWidget.type ?? PerformanceQuotesType.PERFORMANCE;

  const chartData = useMemo(() => {
    return quotes.map(q => ({
      date: new Date(q.time * 1000),
      value:
        selectedPerformanceType === PerformanceQuotesType.TWROR
          ? q.twror * 100
          : selectedPerformanceType === PerformanceQuotesType.MWROR
            ? q.mwror * 100
            : q.price,
    }));
  }, [quotes, selectedPerformanceType]);

  const rangePoints = useMemo(() => {
    const maxQuote = chartData.reduce((max, quote) => (quote.value > max ? quote.value : max), 0);
    const minQuote = chartData.reduce((min, quote) => (quote.value < min ? quote.value : min), maxQuote);
    const range = maxQuote - minQuote;
    return {
      y: {
        min: minQuote - 0.1 * range,
        max: maxQuote + 0.05 * range,
      },
    };
  }, [chartData]);

  const updatePerformanceType = async (type: PerformanceQuotesType) => {
    await updateActorSettingsOptimistically({
      performanceQuotesWidget: {
        type,
      },
    });
  };

  // Create a function that formats the pointer value based on the selected type
  const formatValueForPointer = (value?: LineDataPoint[]) => {
    if (!value?.[0] || !currentQuote) return;

    if (selectedPerformanceType === PerformanceQuotesType.PERFORMANCE) {
      return t('currency', {
        amount: {
          amount: value?.[0].value ?? 0,
          unit: currentQuote.currency || 'EUR',
        },
      });
    } else {
      return t('percent', {
        value: {
          value: (value?.[0].value ?? 0) / 100,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        },
      });
    }
  };

  return (
    <Widget title={title} ready={!isLoading}>
      {/* Header */}
      <View style={styles.header}>
        {/* Performance Type Selector */}
        <View style={styles.performanceSelector}>
          {Object.values(PerformanceQuotesType).map(type => (
            <PerformanceButton
              key={type}
              type={type}
              isActive={selectedPerformanceType === type}
              onPress={() => updatePerformanceType(type)}
            />
          ))}
        </View>
      </View>

      {/* Chart Container */}
      {!isLoading && quotes.length > 0 && (
        <View style={[styles.chartContainer, { backgroundColor: 'transparent' }]}>
          <ReusableLineChart
            series={[{ data: chartData }]}
            height={chartHeight}
            width={screenWidth}
            minValue={rangePoints?.y.min}
            maxValue={rangePoints?.y.max}
            disableScroll={true}
            formatValueForPointer={formatValueForPointer}
            pointerLabelComponent={items => {
              return (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: themeColors.background,
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: themeColors.border,
                    paddingHorizontal: 12,
                    paddingVertical: 8,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.1,
                    shadowRadius: 4,
                    elevation: 3,
                    minWidth: 80,
                  }}
                >
                  <ThemedText size={14} type="semi-bold" style={{ color: themeColors.text }}>
                    {formatValueForPointer(items)}
                  </ThemedText>
                  <ThemedText size={14} type="semi-bold" style={{ color: themeColors.text }}>
                    {t('dateTime.day', {
                      date: new Date((items?.[0] as any)?.date || 0),
                    })}
                  </ThemedText>
                </View>
              );
            }}
          />
        </View>
      )}

      {/* No data state */}
      {!isLoading && quotes.length === 0 && (
        <View className="flex-1 justify-center items-center" style={{ height: chartHeight }}>
          <ThemedText className="text-gray-500 text-lg">{noDataText}</ThemedText>
        </View>
      )}

      {/* Period Selector */}
      <View style={styles.periodSelectorContainer}>
        <View style={[styles.periodButtonsContainer, { paddingHorizontal: 16 }]}>
          {Object.entries(QuoteRange)
            .filter(([k]) => isNaN(+k))
            .sort((a, b) => (a[1] as number) - (b[1] as number))
            .map(([key, value]) => (
              <PeriodButton
                key={key}
                period={value as QuoteRange}
                isActive={selectedPeriod === value}
                onPress={() => setSelectedPeriod(value as QuoteRange)}
              />
            ))}
        </View>
      </View>
    </Widget>
  );
};

const PerformanceButton: React.FC<{ type: PerformanceQuotesType; isActive: boolean; onPress: () => void }> = ({
  type,
  isActive,
  onPress,
}) => {
  const { t } = useTranslation();
  const { colors } = useTheme();

  const getPerformanceTypeLabel = (type: PerformanceQuotesType) => {
    return t('actor.quotes.options.' + (type ?? PerformanceQuotesType.PERFORMANCE));
  };

  return (
    <TouchableOpacity
      style={[styles.performanceButton, { backgroundColor: isActive ? colors.border : colors.inputBackground }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText type={isActive ? 'semi-bold' : 'regular'}>{getPerformanceTypeLabel(type)}</ThemedText>
    </TouchableOpacity>
  );
};

const PeriodButton: React.FC<{ period: QuoteRange; isActive: boolean; onPress: () => void }> = ({
  period,
  isActive,
  onPress,
}) => {
  const { t } = useTranslation();
  const { colors } = useTheme();

  const getPeriodLabel = (period: QuoteRange) => {
    return t(`actor.quotes.radioButton.${QuoteRange[period]}`);
  };

  return (
    <TouchableOpacity
      style={[
        styles.periodButton,
        isActive && [styles.activePeriodButton, { borderWidth: 2, borderColor: colors.primary }],
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <ThemedText size={15} type="semi-bold">
        {getPeriodLabel(period)}
      </ThemedText>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginBottom: 24,
  },
  card: {
    borderRadius: 20,
  },
  header: {
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  performanceSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  performanceButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginHorizontal: 4,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  currentValueContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  chartContainer: {
    backgroundColor: 'transparent',
    marginBottom: 16,
    overflow: 'hidden',
    width: '100%',
    paddingHorizontal: 0,
    marginHorizontal: 0,
    position: 'relative',
    height: 180,
  },
  periodSelectorContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  periodButtonsContainer: {
    flexDirection: 'row',
    borderRadius: 28,
    width: '100%',
    justifyContent: 'space-between',
  },
  periodButton: {
    paddingHorizontal: 18,
    borderRadius: 22,
    paddingVertical: 3,
  },
  activePeriodButton: {},
});

export default QuotesChart;
