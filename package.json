{"name": "@divizend/companion", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "expo start", "dev": "expo start --go", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "prepare": "husky"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@divizend/react-native-calendars": "git+https://github.com/divizend/react-native-calendars.git", "@divizend/react-native-graph": "git+https://github.com/divizend/react-native-graph.git", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.0.4", "@preact/signals-react": "^2.2.0", "@react-native-community/datetimepicker": "8.0.1", "@react-native-picker/picker": "2.7.5", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.8", "@shopify/flash-list": "^1.8.3", "@shopify/react-native-skia": "^1.11.11", "@tanstack/react-query": "^5.52.2", "axios": "^1.7.9", "eslint-plugin-react": "^7.37.2", "event-target-polyfill": "^0.0.4", "expo": "~51.0.39", "expo-auth-session": "~5.5.2", "expo-blur": "^13.0.2", "expo-clipboard": "~6.0.3", "expo-constants": "~16.0.2", "expo-crypto": "~13.0.2", "expo-dev-client": "~4.0.29", "expo-font": "~12.0.9", "expo-linear-gradient": "^13.0.2", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-router": "~3.5.24", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.7", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "formik": "^2.4.6", "i18next": "^23.16.4", "immer": "^10.1.1", "lodash": "^4.17.21", "nativewind": "^4.0.36", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.1.0", "react-native": "0.74.5", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "~2.16.1", "react-native-gifted-charts": "^1.4.58", "react-native-markdown-display": "^7.0.2", "react-native-progress": "^5.0.1", "react-native-purchases": "^8.2.3", "react-native-reanimated": "~3.10.1", "react-native-reanimated-carousel": "^4.0.0-canary.17", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "15.2.0", "react-native-uuid": "^2.0.3", "react-native-web": "~0.19.10", "react-native-webview": "^13.13.1", "reconnecting-websocket": "^4.4.0", "socket.io-client": "^4.7.5", "sse.js": "^2.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.13", "victory-native": "^41.17.4", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9.14.0", "@preact/signals-react-transform": "^0.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/eslint__js": "^8.42.3", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "eslint": "^8.57.0", "eslint-config-expo": "~7.1.2", "husky": "^9.1.6", "jest": "^29.2.1", "jest-expo": "~51.0.3", "lint-staged": "^15.2.10", "prettier": "3.3.3", "react-test-renderer": "18.2.0", "typescript": "~5.3.3", "typescript-eslint": "^8.13.0"}}