import React, { useMemo } from 'react';

import { useQuery } from '@tanstack/react-query';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

import { useUserProfileQuery } from '@/common/queries';
import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';
import { ActorService } from '@/services/actor.service';
import { SecurityAccountSecurity } from '@/types/actor-api.types';

import Widget from './Widget';

export type CurrentPriceWidgetProps = {
  security: SecurityAccountSecurity;
};

export default function CurrentPriceWidget({ security }: CurrentPriceWidgetProps) {
  const { t } = useTranslation();
  const { colors, isDark } = useTheme();
  const { data: profile, isLoading: profileLoading } = useUserProfileQuery();
  const currency = profile?.flags.currency;

  // Get price and WKN/ISIN
  const { data, isLoading } = useQuery({
    queryKey: ['companyPerformance', security.isin, currency],
    queryFn: () => ActorService.getCompanyPerformance(security, currency),
    enabled: !!security.isin && !!currency,
  });
  console.log(security);
  const displayValue = useMemo(() => {
    return data?.quote?.price
      ? t('currency', {
          amount: {
            amount: data.quote.price ?? 0,
            unit: data?.quote?.currency ?? currency,
            options: {
              notation: data.quote.price.toString().length > 6 ? 'compact' : undefined,
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            },
          },
        })
      : t('actor.sharePriceWidget.noData');
  }, [data, currency, t]);
  console.log(data);
  return (
    <Widget
      title={t('actor.sharePriceWidget.title')}
      ready={!isLoading && !profileLoading && !!currency}
      styles={{
        container: { flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 10 },
      }}
    >
      <ExpoLinearGradient
        colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ paddingVertical: 20, paddingHorizontal: 16, width: '100%', alignItems: 'center' }}
      >
        {/* Split value and currency for coloring */}
        {data?.quote?.price ? (
          <View style={{ flexDirection: 'row', alignItems: 'flex-end', justifyContent: 'center' }}>
            <ThemedText size={40} type="bold" style={{ color: colors.primary, lineHeight: 48 }}>
              {/* Extract currency and value */}
              {(() => {
                const price = data.quote.price ?? 0;
                const unit = data?.quote?.currency ?? currency ?? '';
                // Format with 2 decimals, no currency
                return price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
              })()}
            </ThemedText>
            <ThemedText size={22} type="bold" style={{ color: colors.text, marginLeft: 2, marginBottom: 4 }}>
              {' '}
              {data?.quote?.currency ?? currency ?? ''}
            </ThemedText>
          </View>
        ) : (
          <ThemedText size={40} type="bold">
            {displayValue}
          </ThemedText>
        )}
      </ExpoLinearGradient>
      <View
        style={{
          flexDirection: 'column',
          width: '100%',
          justifyContent: 'flex-start',
          paddingHorizontal: 16,
          marginTop: 6,
        }}
      >
        <ThemedText type="bold">
          ISIN: <ThemedText type="regular">{security.isin}</ThemedText>
        </ThemedText>
        {data?.wkn && (
          <ThemedText type="bold">
            WKN: <ThemedText type="regular">{data.wkn}</ThemedText>
          </ThemedText>
        )}
      </View>
    </Widget>
  );
}
