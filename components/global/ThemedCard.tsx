import React from 'react';

import { Image, ImageSourcePropType, StyleSheet, TouchableOpacity, TouchableOpacityProps, View } from 'react-native';

import { useTheme } from '@/context/ThemeContext';

import { ThemedText } from './ThemedText';

interface ThemedCardProps extends TouchableOpacityProps {
  title: string;
  description: string;
  imageSource: ImageSourcePropType;
  imageStyle?: object;
  biggerImage?: boolean;
}

export const ThemedCard: React.FC<ThemedCardProps> = ({
  title,
  description,
  imageSource,
  imageStyle,
  biggerImage = false,
  style,
  ...props
}) => {
  const { colors, isLight, isDark } = useTheme();

  const styles = StyleSheet.create({
    container: {
      borderRadius: 30,
      backgroundColor: colors.background,
      paddingHorizontal: 20,
      paddingVertical: biggerImage ? 30 : 20,
      marginBottom: 24,
      borderWidth: 1,
      borderColor: colors.border,
      position: 'relative', // Add this to establish stacking context
      overflow: 'hidden', // Add this to clip the image within card bounds
    },
    content: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      zIndex: 2, // Put content above the image
      position: 'relative', // Needed for zIndex to work
      gap: 16,
    },
    textContainer: {
      flex: 1,
    },
    title: {
      textAlign: 'center',
    },
    imageContainer: {
      position: 'absolute',
      top: 0,
      right: -5,
      zIndex: 1, // Behind the content but above the card background
      alignItems: 'center',
      justifyContent: 'center',
    },
    defaultImage: {
      borderRadius: 30,

      width: 80,
      height: 80,
    },
  });

  return (
    <TouchableOpacity style={[styles.container, style]} {...props}>
      <View style={styles.imageContainer}>
        <Image source={imageSource} style={[styles.defaultImage, imageStyle]} resizeMode="contain" />
      </View>
      <View style={styles.content}>
        <View style={styles.textContainer}>
          {biggerImage && title.split(' ').length === 2 ? (
            title.split(' ').map((word, index) => (
              <ThemedText key={index} type="bold" size={24} style={styles.title}>
                {word}
              </ThemedText>
            ))
          ) : (
            <ThemedText type="bold" size={24} style={styles.title}>
              {title}
            </ThemedText>
          )}
          <ThemedText style={{ textAlign: 'center', marginTop: 12 }}>{description}</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );
};
