import React from 'react';

import { StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { useTheme } from '@/context/ThemeContext';

interface ClaimButtonProps {
  onPress?: () => void;
  title?: string;
  disabled?: boolean;
}

export default function ClaimButton({ onPress, title = 'CLAIM NOW', disabled = false }: ClaimButtonProps) {
  const { colors } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.claimButton,
        {
          backgroundColor: disabled ? colors.text + '40' : colors.primary,
        },
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={disabled}
    >
      <ThemedText size={16} type="semi-bold" style={[styles.claimButtonText, { color: colors.background }]}>
        {title}
      </ThemedText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  claimButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  claimButtonText: {
    letterSpacing: 0.5,
  },
});
