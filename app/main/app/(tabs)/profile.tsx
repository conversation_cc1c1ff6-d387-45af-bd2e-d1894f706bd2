import React from 'react';

import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';

import { ScrollScreen } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';

import SettingsView from '../../settings';

export default function profile() {
  const { t } = useTranslation();

  return (
    <ScrollScreen contentContainerStyle={{ paddingHorizontal: 0 }}>
      <ThemedText h1 className="font-bold" style={{ marginHorizontal: 25, paddingTop: 10 }}>
        {t('settings.pages.settings')}
      </ThemedText>
      <SettingsView />
    </ScrollScreen>
  );
}
const styles = StyleSheet.create({});
