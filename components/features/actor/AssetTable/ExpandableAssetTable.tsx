import React from 'react';

import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { View } from 'react-native';

import Widget from '@/components/features/actor/Widget';
import PeriodButton from '@/components/global/PeriodButton';
import { useTheme } from '@/context/ThemeContext';

import AssetTableRow from './AssetTableRow';

export interface AssetTableItem {
  id: string;
  icon: string;
  name: string;
  subtitle: string;
  percentage?: string;
  value: string;
  percentageColor?: string;
  valueUnderline?: boolean;
  rightLabel?: string;
  rightLabelColor?: string;
}

interface ExpandableAssetTableProps {
  title: string;
  data: AssetTableItem[];
  collapsedCount?: number;
  expandedTitle?: string;
  isTopAsset?: boolean;
}

const ExpandableAssetTable: React.FC<ExpandableAssetTableProps> = ({
  title,
  data,
  collapsedCount = 3,
  expandedTitle,
  isTopAsset = false,
}) => {
  const { isDark } = useTheme();
  const collapsedData: AssetTableItem[] = data.slice(0, collapsedCount);
  const renderRow = (item: AssetTableItem, index: number) => {
    // Top assets: add titleValue and isTopAsset

    const rowProps = isTopAsset ? { ...item, titleValue: item.value, isTopAsset } : item;
    if (index % 2 === 0) {
      return (
        <ExpoLinearGradient
          key={item.id}
          colors={isDark ? ['#1d2b39', '#1a202c', '#1d2b39'] : ['#f7fafc', '#ffffff', '#f7fafc']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={{}}
        >
          <AssetTableRow {...rowProps} />
        </ExpoLinearGradient>
      );
    }
    return <AssetTableRow key={item.id} {...rowProps} />;
  };

  // Static period data with stateful selection
  const periodOptions = [
    { key: 'D', label: 'D' },
    { key: 'W', label: 'W' },
    { key: 'M', label: 'M' },
    { key: 'Y', label: 'Y' },
    { key: 'ALL', label: 'ALL' },
  ];
  const [selectedPeriod, setSelectedPeriod] = React.useState('Y');

  return (
    <Widget
      isExpandable
      title={title}
      ready={true}
      expandedContent={
        <View>
          {expandedTitle || null}
          {data.map((item, idx) => renderRow(item, idx))}
        </View>
      }
    >
      <View>
        {collapsedData.map((item, idx) => renderRow(item, idx))}
        <View style={{ flexDirection: 'row', justifyContent: 'center', marginTop: 8 }}>
          {periodOptions.map(period => (
            <PeriodButton
              key={period.key}
              period={period.key}
              isActive={period.key === selectedPeriod}
              onPress={() => {
                setSelectedPeriod(period.key);
                console.log('Selected period:', period.key);
              }}
              label={period.label}
            />
          ))}
        </View>
      </View>
    </Widget>
  );
};

export default ExpandableAssetTable;
