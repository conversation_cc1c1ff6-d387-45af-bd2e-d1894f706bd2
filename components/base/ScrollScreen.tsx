import React, { forwardRef, useRef } from 'react';

import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from 'expo-router';
import { ScrollView as NativeScrollView, ScrollViewProps } from 'react-native';

import { useTheme } from '@/context/ThemeContext';
import { isHeaderVisible } from '@/signals/app.signal';

type Props = ScrollViewProps;

export type ScrollScreenRef = NativeScrollView;

export const ScrollScreen = forwardRef<NativeScrollView, Props>(
  ({ children, style, contentContainerStyle, ...props }, ref) => {
    const scrollY = useRef<number>();
    const isFocused = useRef<boolean>(true);
    const { colors, isLight, isDark } = useTheme();

    const paddingTop = 100; // Adjust this value based on your header height

    useFocusEffect(() => {
      isFocused.current = true;
      isHeaderVisible.value = (scrollY.current ?? 0) > 50;

      return () => {
        isFocused.current = false;
        isHeaderVisible.value = false;
      };
    });

    return (
      <>
        <LinearGradient
          colors={colors.linearGradient}
          style={[
            {
              backgroundColor: colors.background,
              position: 'absolute',
              top: -paddingTop,
              left: 0,
              right: 0,
              bottom: 0,
            },
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        ></LinearGradient>
        <NativeScrollView
          {...props}
          ref={ref}
          style={[
            {
              flex: 1,
              overflow: 'visible',
            },
            style,
          ]}
          contentContainerStyle={[
            {
              paddingHorizontal: 20,
              overflow: 'visible',
              paddingBottom: 50,
            },
            contentContainerStyle,
          ]}
          showsVerticalScrollIndicator={false}
          onScroll={event => {
            const y = event.nativeEvent.contentOffset.y;
            scrollY.current = y;

            if (!isFocused.current) return;

            if (y > 50) {
              isHeaderVisible.value = true;
            } else {
              isHeaderVisible.value = false;
            }
            props.onScroll?.(event);
          }}
        >
          {children}
        </NativeScrollView>
      </>
    );
  },
);
